<?php
/**
 * إصلاح مشكلة PHPMailer
 */

echo "🔧 إصلاح مشكلة PHPMailer\n";
echo str_repeat("=", 50) . "\n";

// تحديد BASE_PATH
define('BASE_PATH', __DIR__);

// 1. تنظيف cache PHP
echo "🧹 تنظيف cache PHP...\n";
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✅ تم تنظيف OPcache\n";
} else {
    echo "ℹ️ OPcache غير مفعل\n";
}

// 2. تنظيف cache الجلسة
echo "\n🧹 تنظيف cache الجلسة...\n";
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
    echo "✅ تم تنظيف الجلسة\n";
} else {
    echo "ℹ️ لا توجد جلسة نشطة\n";
}

// 3. فحص مسارات Composer
echo "\n📦 فحص مسارات Composer...\n";
$composer_paths = [
    dirname(BASE_PATH) . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php'
];

$composer_found = false;
foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        echo "✅ وجد Composer في: $path\n";
        require_once $path;
        $composer_found = true;
        break;
    } else {
        echo "❌ لم يوجد في: $path\n";
    }
}

if (!$composer_found) {
    echo "❌ لم يتم العثور على Composer autoloader\n";
    exit;
}

// 4. فحص PHPMailer
echo "\n📧 فحص PHPMailer...\n";
if (class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer متاح\n";
    
    try {
        $mailer = new \PHPMailer\PHPMailer\PHPMailer();
        echo "✅ يمكن إنشاء instance من PHPMailer\n";
        echo "📧 Version: " . $mailer::VERSION . "\n";
        
        // فحص الكلاسات المطلوبة
        $required_classes = [
            '\PHPMailer\PHPMailer\PHPMailer',
            '\PHPMailer\PHPMailer\SMTP',
            '\PHPMailer\PHPMailer\Exception'
        ];
        
        foreach ($required_classes as $class) {
            if (class_exists($class)) {
                echo "✅ $class متاح\n";
            } else {
                echo "❌ $class غير متاح\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء PHPMailer: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PHPMailer غير متاح\n";
}

// 5. فحص ملفات النظام
echo "\n📁 فحص ملفات النظام...\n";
$system_files = [
    BASE_PATH . '/App/Helpers/email.php',
    BASE_PATH . '/loader_new.php',
    BASE_PATH . '/config/config.php'
];

foreach ($system_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود\n";
    } else {
        echo "❌ $file غير موجود\n";
    }
}

// 6. إنشاء ملف اختبار PHPMailer جديد
echo "\n🧪 إنشاء ملف اختبار PHPMailer...\n";

$test_content = '<?php
/**
 * اختبار PHPMailer المباشر
 */

// تحميل Composer autoloader
$composer_paths = [
    __DIR__ . "/../vendor/autoload.php",
    __DIR__ . "/../../vendor/autoload.php"
];

foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

// اختبار PHPMailer
if (class_exists("\\PHPMailer\\PHPMailer\\PHPMailer")) {
    echo "✅ PHPMailer متاح\\n";
    
    try {
        $mail = new \\PHPMailer\\PHPMailer\\PHPMailer(true);
        echo "✅ تم إنشاء instance بنجاح\\n";
        echo "📧 Version: " . $mail::VERSION . "\\n";
    } catch (Exception $e) {
        echo "❌ خطأ: " . $e->getMessage() . "\\n";
    }
} else {
    echo "❌ PHPMailer غير متاح\\n";
}
?>';

file_put_contents(BASE_PATH . '/test-phpmailer-direct.php', $test_content);
echo "✅ تم إنشاء ملف test-phpmailer-direct.php\n";

// 7. تشغيل الاختبار المباشر
echo "\n🚀 تشغيل الاختبار المباشر...\n";
$output = shell_exec('php ' . BASE_PATH . '/test-phpmailer-direct.php 2>&1');
echo $output;

// 8. فحص متغيرات البيئة
echo "\n🔐 فحص متغيرات البيئة...\n";
if (file_exists(BASE_PATH . '/.env')) {
    echo "✅ ملف .env موجود\n";
    
    // تحميل متغيرات البيئة
    if (file_exists(BASE_PATH . '/App/Core/EnvLoader.php')) {
        require_once BASE_PATH . '/App/Core/EnvLoader.php';
        require_once BASE_PATH . '/App/Helpers/env_helper.php';
        
        App\Core\EnvLoader::load();
        echo "✅ تم تحميل متغيرات البيئة\n";
        
        // فحص متغيرات الإيميل
        $email_vars = ['MAIL_HOST', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_PASSWORD'];
        foreach ($email_vars as $var) {
            $value = env($var);
            if (!empty($value)) {
                echo "✅ $var محدد\n";
            } else {
                echo "❌ $var غير محدد\n";
            }
        }
    }
} else {
    echo "❌ ملف .env غير موجود\n";
}

// 9. إعادة تحميل Composer autoloader
echo "\n🔄 إعادة تحميل Composer autoloader...\n";
$composer_dir = dirname(BASE_PATH) . '/vendor';
if (is_dir($composer_dir)) {
    echo "✅ مجلد vendor موجود\n";
    
    // تشغيل composer dump-autoload
    $composer_output = shell_exec('cd ' . dirname(BASE_PATH) . ' && composer dump-autoload --optimize 2>&1');
    echo "📦 خرج Composer:\n$composer_output\n";
} else {
    echo "❌ مجلد vendor غير موجود\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 الخلاصة:\n";

// فحص نهائي
if (class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    echo "🎉 PHPMailer يعمل بشكل صحيح!\n";
    echo "✅ يمكن الآن استخدام ميزة نسيان كلمة المرور\n";
} else {
    echo "❌ PHPMailer ما زال لا يعمل\n";
    echo "💡 جرب:\n";
    echo "1. تشغيل: composer install\n";
    echo "2. تشغيل: composer dump-autoload\n";
    echo "3. التأكد من وجود مجلد vendor\n";
}

echo "\nتم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "\n";
?>
