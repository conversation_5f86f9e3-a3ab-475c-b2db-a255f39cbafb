<?php
/**
 * اختبار ميزة نسيان كلمة المرور
 */

echo "🔐 اختبار ميزة نسيان كلمة المرور\n";
echo str_repeat("=", 50) . "\n";

// تحديد BASE_PATH
define('BASE_PATH', __DIR__);

// تحميل Composer autoloader
$composer_paths = [
    dirname(BASE_PATH) . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../vendor/autoload.php'
];

$composer_loaded = false;
foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        echo "✅ تم تحميل Composer autoloader\n";
        $composer_loaded = true;
        break;
    }
}

if (!$composer_loaded) {
    echo "❌ Composer autoloader غير موجود\n";
    exit;
}

// تحميل إعدادات البيئة
if (file_exists(BASE_PATH . '/App/Core/EnvLoader.php')) {
    require_once BASE_PATH . '/App/Core/EnvLoader.php';
    require_once BASE_PATH . '/App/Helpers/env_helper.php';

    // تحميل ملف .env
    App\Core\EnvLoader::load();
    echo "✅ تم تحميل إعدادات البيئة\n";
}

// تحديد الثوابت
define('MAIL_HOST', env('MAIL_HOST', 'localhost'));
define('MAIL_PORT', env('MAIL_PORT', 587));
define('MAIL_USERNAME', env('MAIL_USERNAME', ''));
define('MAIL_PASSWORD', env('MAIL_PASSWORD', ''));
define('MAIL_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', ''));
define('MAIL_FROM_NAME', env('MAIL_FROM_NAME', ''));
define('APP_URL', env('APP_URL', 'http://localhost'));

// تحميل الدوال المساعدة
require_once BASE_PATH . '/App/Helpers/functions.php';
require_once BASE_PATH . '/App/Helpers/email.php';

echo "\n📧 اختبار إرسال إيميل نسيان كلمة المرور:\n";

// بيانات تجريبية
$test_email = '<EMAIL>';
$test_token = bin2hex(random_bytes(32));
$language = 'ar';

echo "📧 الإيميل المستهدف: $test_email\n";
echo "🔑 الرمز المولد: " . substr($test_token, 0, 16) . "...\n";
echo "🌐 اللغة: $language\n";

// اختبار دالة send_password_reset_email
if (function_exists('send_password_reset_email')) {
    echo "\n🚀 محاولة إرسال إيميل نسيان كلمة المرور...\n";

    try {
        $result = send_password_reset_email($test_email, $test_token, $language);

        if ($result) {
            echo "✅ تم إرسال الإيميل بنجاح!\n";
            echo "📧 تم إرسال رابط إعادة تعيين كلمة المرور إلى: $test_email\n";
            echo "🔗 الرابط: " . base_url('reset-password/' . $test_token) . "\n";
        } else {
            echo "❌ فشل في إرسال الإيميل\n";
            echo "💡 تحقق من:\n";
            echo "   - إعدادات SMTP\n";
            echo "   - صحة بيانات الاعتماد\n";
            echo "   - الاتصال بالإنترنت\n";
        }

    } catch (Exception $e) {
        echo "❌ خطأ في إرسال الإيميل: " . $e->getMessage() . "\n";
    }

} else {
    echo "❌ دالة send_password_reset_email غير متاحة\n";
}

// اختبار إرسال إيميل مباشر
echo "\n📧 اختبار إرسال إيميل مباشر:\n";

try {
    $subject = "اختبار نسيان كلمة المرور";
    $body = "
    <div dir='rtl' style='font-family: Arial, sans-serif; padding: 20px;'>
        <h2>اختبار إعادة تعيين كلمة المرور</h2>
        <p>هذا اختبار لميزة نسيان كلمة المرور.</p>
        <p>الرمز: $test_token</p>
        <p>الرابط: " . base_url('reset-password/' . $test_token) . "</p>
    </div>";

    $direct_result = send_email($test_email, $subject, $body);

    if ($direct_result) {
        echo "✅ تم إرسال الإيميل المباشر بنجاح!\n";
    } else {
        echo "❌ فشل في إرسال الإيميل المباشر\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ في الإرسال المباشر: " . $e->getMessage() . "\n";
}

// فحص سجل الأخطاء
echo "\n📋 فحص سجل الأخطاء:\n";
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    echo "📄 ملف سجل الأخطاء: $error_log_file\n";

    // قراءة آخر 10 أسطر من سجل الأخطاء
    $lines = file($error_log_file);
    $recent_lines = array_slice($lines, -10);

    echo "📝 آخر الأخطاء:\n";
    foreach ($recent_lines as $line) {
        if (strpos($line, 'PHPMailer') !== false || strpos($line, 'Email') !== false) {
            echo "⚠️ " . trim($line) . "\n";
        }
    }
} else {
    echo "ℹ️ لا يوجد ملف سجل أخطاء محدد\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 الخلاصة:\n";

if (function_exists('send_password_reset_email') && class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ جميع المكونات متوفرة\n";
    echo "✅ PHPMailer مثبت ويعمل\n";
    echo "✅ دالة إرسال الإيميل متاحة\n";
    echo "✅ إعدادات SMTP مكتملة\n";
    echo "\n💡 إذا لم يصل الإيميل:\n";
    echo "1. تحقق من مجلد الرسائل غير المرغوب فيها\n";
    echo "2. تأكد من صحة كلمة مرور التطبيق لـ Gmail\n";
    echo "3. تفعيل 'Less secure app access' إذا لزم الأمر\n";
    echo "4. استخدام App Password بدلاً من كلمة المرور العادية\n";
} else {
    echo "❌ هناك مكونات مفقودة\n";
}

echo "\nتم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "\n";
?>
