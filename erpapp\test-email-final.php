<?php
/**
 * اختبار نهائي لميزة نسيان كلمة المرور
 */

echo "🔐 اختبار نهائي لميزة نسيان كلمة المرور\n";
echo str_repeat("=", 50) . "\n";

// تحديد BASE_PATH
define('BASE_PATH', __DIR__);

// تحميل Composer autoloader
$composer_paths = [
    dirname(BASE_PATH) . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php'
];

foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        echo "✅ تم تحميل Composer autoloader\n";
        break;
    }
}

// تحميل الإعدادات
if (file_exists(BASE_PATH . '/App/Core/EnvLoader.php')) {
    require_once BASE_PATH . '/App/Core/EnvLoader.php';
    require_once BASE_PATH . '/App/Helpers/env_helper.php';
    
    App\Core\EnvLoader::load();
    echo "✅ تم تحميل إعدادات البيئة\n";
}

// تحديد الثوابت
define('MAIL_HOST', env('MAIL_HOST', 'localhost'));
define('MAIL_PORT', env('MAIL_PORT', 587));
define('MAIL_USERNAME', env('MAIL_USERNAME', ''));
define('MAIL_PASSWORD', env('MAIL_PASSWORD', ''));
define('MAIL_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', ''));
define('MAIL_FROM_NAME', env('MAIL_FROM_NAME', ''));
define('APP_URL', env('APP_URL', 'http://localhost'));

// تحميل ملف الإيميل
require_once BASE_PATH . '/App/Helpers/email.php';

echo "\n📧 فحص إعدادات الإيميل:\n";
echo "MAIL_HOST: " . MAIL_HOST . "\n";
echo "MAIL_PORT: " . MAIL_PORT . "\n";
echo "MAIL_FROM_ADDRESS: " . MAIL_FROM_ADDRESS . "\n";
echo "MAIL_FROM_NAME: " . MAIL_FROM_NAME . "\n";

// اختبار PHPMailer
echo "\n📦 فحص PHPMailer:\n";
if (class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer متاح\n";
    
    try {
        $mailer = new \PHPMailer\PHPMailer\PHPMailer();
        echo "✅ يمكن إنشاء instance من PHPMailer\n";
        echo "📧 Version: " . $mailer::VERSION . "\n";
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء PHPMailer: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PHPMailer غير متاح\n";
}

// اختبار الدوال
echo "\n🧪 اختبار الدوال:\n";
if (function_exists('send_email')) {
    echo "✅ دالة send_email متاحة\n";
} else {
    echo "❌ دالة send_email غير متاحة\n";
}

if (function_exists('send_password_reset_email')) {
    echo "✅ دالة send_password_reset_email متاحة\n";
} else {
    echo "❌ دالة send_password_reset_email غير متاحة\n";
}

// اختبار إرسال إيميل نسيان كلمة المرور
echo "\n🔐 اختبار إرسال إيميل نسيان كلمة المرور:\n";

$test_email = '<EMAIL>';
$test_token = bin2hex(random_bytes(32));

echo "📧 الإيميل المستهدف: $test_email\n";
echo "🔑 الرمز المولد: " . substr($test_token, 0, 16) . "...\n";

try {
    echo "\n🚀 محاولة إرسال إيميل نسيان كلمة المرور...\n";
    $reset_result = send_password_reset_email($test_email, $test_token, 'ar');
    
    if ($reset_result) {
        echo "✅ تم إرسال إيميل نسيان كلمة المرور بنجاح!\n";
        echo "🔗 الرابط: " . APP_URL . '/reset-password/' . $test_token . "\n";
    } else {
        echo "❌ فشل في إرسال إيميل نسيان كلمة المرور\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إرسال إيميل نسيان كلمة المرور: " . $e->getMessage() . "\n";
}

// اختبار إرسال إيميل بسيط
echo "\n📧 اختبار إرسال إيميل بسيط:\n";

try {
    $subject = "اختبار نظام الإيميل";
    $body = "
    <div dir='rtl' style='font-family: Arial, sans-serif; padding: 20px;'>
        <h2>اختبار نظام الإيميل</h2>
        <p>هذا اختبار للتأكد من عمل نظام الإيميل بشكل صحيح.</p>
        <p>الوقت: " . date('Y-m-d H:i:s') . "</p>
        <p>الرمز التجريبي: $test_token</p>
    </div>";
    
    echo "🚀 محاولة إرسال إيميل بسيط...\n";
    $simple_result = send_email($test_email, $subject, $body);
    
    if ($simple_result) {
        echo "✅ تم إرسال الإيميل البسيط بنجاح!\n";
    } else {
        echo "❌ فشل في إرسال الإيميل البسيط\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إرسال الإيميل البسيط: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 الخلاصة النهائية:\n";

$all_good = true;
$issues = [];

if (!class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    $issues[] = "PHPMailer غير متاح";
    $all_good = false;
}

if (!function_exists('send_email')) {
    $issues[] = "دالة send_email غير متاحة";
    $all_good = false;
}

if (!function_exists('send_password_reset_email')) {
    $issues[] = "دالة send_password_reset_email غير متاحة";
    $all_good = false;
}

if (empty(MAIL_HOST) || empty(MAIL_USERNAME) || empty(MAIL_PASSWORD)) {
    $issues[] = "إعدادات SMTP غير مكتملة";
    $all_good = false;
}

if ($all_good) {
    echo "🎉 ممتاز! جميع المكونات تعمل بشكل صحيح!\n";
    echo "✅ PHPMailer مثبت ويعمل\n";
    echo "✅ دوال الإيميل متاحة\n";
    echo "✅ إعدادات SMTP مكتملة\n";
    echo "✅ ميزة نسيان كلمة المرور جاهزة للاستخدام\n";
    echo "\n🚀 يمكنك الآن استخدام النظام بثقة!\n";
    echo "\n💡 نصائح:\n";
    echo "1. استخدم إيميل حقيقي للاختبار الفعلي\n";
    echo "2. تحقق من مجلد الرسائل غير المرغوب فيها\n";
    echo "3. تأكد من استخدام App Password لـ Gmail\n";
} else {
    echo "⚠️ هناك مشاكل تحتاج إصلاح:\n";
    foreach ($issues as $issue) {
        echo "❌ $issue\n";
    }
}

echo "\nتم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "\n";
?>
