<?php
/**
 * تنظيف شامل للنظام
 */

echo "🧹 تنظيف شامل للنظام\n";
echo str_repeat("=", 50) . "\n";

// تحديد BASE_PATH
define('BASE_PATH', __DIR__);

// 1. تنظيف ملفات cache
echo "🗑️ تنظيف ملفات cache...\n";

$cache_dirs = [
    BASE_PATH . '/storage/cache',
    BASE_PATH . '/storage/sessions',
    BASE_PATH . '/storage/logs'
];

foreach ($cache_dirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        $cleaned = 0;
        foreach ($files as $file) {
            if (is_file($file) && basename($file) !== '.gitkeep') {
                unlink($file);
                $cleaned++;
            }
        }
        echo "✅ تم تنظيف $cleaned ملف من $dir\n";
    } else {
        echo "ℹ️ المجلد غير موجود: $dir\n";
    }
}

// 2. البحث عن ملفات PHPMailer القديمة
echo "\n🔍 البحث عن ملفات PHPMailer القديمة...\n";

$search_dirs = [
    BASE_PATH,
    dirname(BASE_PATH),
    '/home1/qqoshqmy/public_html' // المسار من رسالة الخطأ
];

$old_phpmailer_found = false;
foreach ($search_dirs as $dir) {
    if (is_dir($dir)) {
        $phpmailer_dir = $dir . '/phpmailer';
        if (is_dir($phpmailer_dir)) {
            echo "⚠️ وجد مجلد PHPMailer قديم في: $phpmailer_dir\n";
            $old_phpmailer_found = true;
        }
    }
}

if (!$old_phpmailer_found) {
    echo "✅ لم يتم العثور على ملفات PHPMailer قديمة\n";
}

// 3. فحص ملفات include/require القديمة
echo "\n🔍 فحص ملفات include/require القديمة...\n";

$php_files = glob(BASE_PATH . '/**/*.php', GLOB_BRACE);
$old_includes_found = false;

foreach ($php_files as $file) {
    if (is_file($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'phpmailer/src') !== false || 
            strpos($content, '/home1/qqoshqmy/public_html/phpmailer') !== false) {
            echo "⚠️ وجد مرجع قديم في: $file\n";
            $old_includes_found = true;
        }
    }
}

if (!$old_includes_found) {
    echo "✅ لم يتم العثور على مراجع قديمة\n";
}

// 4. إنشاء ملف email.php جديد ونظيف
echo "\n🔧 إنشاء ملف email.php جديد...\n";

$new_email_content = '<?php
/**
 * Email Helper Functions - Clean Version
 */

/**
 * Send email using PHPMailer
 */
function send_email($to, $subject, $body, $altBody = "", $attachments = []) {
    // التحقق من وجود PHPMailer
    if (!class_exists("\\PHPMailer\\PHPMailer\\PHPMailer")) {
        error_log("PHPMailer class not found. Make sure Composer autoloader is loaded.");
        return false;
    }

    try {
        // استخدام PHPMailer من Composer autoloader
        $mail = new \\PHPMailer\\PHPMailer\\PHPMailer(true);

        // Server settings
        $mail->CharSet = "UTF-8";
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = \\PHPMailer\\PHPMailer\\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = MAIL_PORT;
        
        // إعدادات SSL إضافية لحل مشكلة الشهادة
        $mail->SMTPOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
                "allow_self_signed" => true
            )
        );

        // Recipients
        $mail->setFrom(MAIL_FROM_ADDRESS, MAIL_FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        if (!empty($altBody)) {
            $mail->AltBody = $altBody;
        }

        // Attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // Send the email
        $mail->send();
        return true;
    } catch (\\PHPMailer\\PHPMailer\\Exception $e) {
        error_log("PHPMailer Error: " . $e->getMessage());
        return false;
    } catch (\\Exception $e) {
        error_log("General email sending error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send password reset email
 */
function send_password_reset_email($email, $token, $language = "ar") {
    // Set language
    if ($language == "العربية") {
        $language = "ar";
    } elseif ($language == "English") {
        $language = "en";
    }

    // Generate reset link
    $reset_link = base_url("reset-password/" . $token);

    // Email subject
    $subject = __("استعادة كلمة المرور");

    // Email body
    $body = "
    <div dir=\"" . ($language == "ar" ? "rtl" : "ltr") . "\" style=\"font-family: Arial, sans-serif; padding: 20px; text-align: " . ($language == "ar" ? "right" : "left") . ";\">
        <h2>" . __("استعادة كلمة المرور") . "</h2>
        <p>" . __("لقد تلقينا طلبًا لإعادة تعيين كلمة المرور الخاصة بك.") . "</p>
        <p>" . __("لإعادة تعيين كلمة المرور، انقر على الزر أدناه:") . "</p>
        <p style=\"text-align: center; margin: 30px 0;\">
            <a href=\"" . $reset_link . "\"
               style=\"background-color: #2196F3;
                      color: white;
                      padding: 12px 30px;
                      text-decoration: none;
                      border-radius: 5px;
                      display: inline-block;\">
                " . __("إعادة تعيين كلمة المرور") . "
            </a>
        </p>
        <p>" . __("ينتهي هذا الرابط خلال ساعة واحدة من وقت إرساله.") . "</p>
    </div>";

    // Send email
    return send_email($email, $subject, $body);
}
?>';

// حفظ الملف الجديد
file_put_contents(BASE_PATH . '/App/Helpers/email_clean.php', $new_email_content);
echo "✅ تم إنشاء ملف email_clean.php\n";

// 5. نسخ احتياطي من الملف القديم
echo "\n💾 إنشاء نسخة احتياطية...\n";
if (file_exists(BASE_PATH . '/App/Helpers/email.php')) {
    copy(BASE_PATH . '/App/Helpers/email.php', BASE_PATH . '/App/Helpers/email_backup.php');
    echo "✅ تم إنشاء نسخة احتياطية: email_backup.php\n";
}

// 6. استبدال الملف القديم
echo "\n🔄 استبدال الملف القديم...\n";
if (file_exists(BASE_PATH . '/App/Helpers/email_clean.php')) {
    copy(BASE_PATH . '/App/Helpers/email_clean.php', BASE_PATH . '/App/Helpers/email.php');
    echo "✅ تم استبدال email.php بالنسخة النظيفة\n";
}

// 7. اختبار الملف الجديد
echo "\n🧪 اختبار الملف الجديد...\n";

// تحميل Composer
$composer_paths = [
    dirname(BASE_PATH) . '/vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php'
];

foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

// تحميل الإعدادات
if (file_exists(BASE_PATH . '/App/Core/EnvLoader.php')) {
    require_once BASE_PATH . '/App/Core/EnvLoader.php';
    require_once BASE_PATH . '/App/Helpers/env_helper.php';
    require_once BASE_PATH . '/App/Helpers/functions.php';
    
    App\Core\EnvLoader::load();
    
    define('MAIL_HOST', env('MAIL_HOST', 'localhost'));
    define('MAIL_PORT', env('MAIL_PORT', 587));
    define('MAIL_USERNAME', env('MAIL_USERNAME', ''));
    define('MAIL_PASSWORD', env('MAIL_PASSWORD', ''));
    define('MAIL_FROM_ADDRESS', env('MAIL_FROM_ADDRESS', ''));
    define('MAIL_FROM_NAME', env('MAIL_FROM_NAME', ''));
    
    // تحميل الملف الجديد
    require_once BASE_PATH . '/App/Helpers/email.php';
    
    // اختبار الدالة
    if (function_exists('send_email')) {
        echo "✅ دالة send_email متاحة\n";
        
        if (class_exists('\\PHPMailer\\PHPMailer\\PHPMailer')) {
            echo "✅ PHPMailer متاح\n";
        } else {
            echo "❌ PHPMailer غير متاح\n";
        }
    } else {
        echo "❌ دالة send_email غير متاحة\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎉 تم تنظيف النظام بنجاح!\n";
echo "✅ ملف email.php تم تحديثه\n";
echo "✅ تم إنشاء نسخة احتياطية\n";
echo "✅ تم تنظيف ملفات cache\n";
echo "\n💡 جرب الآن استخدام ميزة نسيان كلمة المرور\n";

echo "\nتم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "\n";
?>
