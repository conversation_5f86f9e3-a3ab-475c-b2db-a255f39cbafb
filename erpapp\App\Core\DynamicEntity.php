<?php
namespace App\Core;

use PDO;
use Exception;

/**
 * نظام الكيانات المرنة (Dynamic Entities)
 * يسمح بإنشاء أي نوع من البيانات بدون الحاجة لجداول ثابتة
 */
class DynamicEntity
{
    protected $db;
    protected $entity_id;
    protected $entity_config;
    protected $fields_config;

    public function __construct($entity_code = null, $company_id = null)
    {
        global $db;
        $this->db = $db;

        if ($entity_code && $company_id) {
            $this->loadEntity($entity_code, $company_id);
        }
    }

    /**
     * تحميل كيان موجود
     */
    public function loadEntity($entity_code, $company_id)
    {
        $sql = "SELECT * FROM dynamic_entities
                WHERE entity_code = ? AND company_id = ? AND is_active = 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_code, $company_id]);
        $this->entity_config = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$this->entity_config) {
            throw new Exception("الكيان غير موجود: $entity_code");
        }

        $this->entity_id = $this->entity_config['entity_id'];
        $this->loadFields();

        return $this;
    }

    /**
     * تحميل حقول الكيان
     */
    protected function loadFields()
    {
        $sql = "SELECT * FROM dynamic_fields
                WHERE entity_id = ? AND is_active = 1
                ORDER BY display_order, field_id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->entity_id]);
        $this->fields_config = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تنظيم الحقول بالكود للوصول السريع
        $fields_by_code = [];
        foreach ($this->fields_config as $field) {
            $fields_by_code[$field['field_code']] = $field;
        }
        $this->fields_config = $fields_by_code;
    }

    /**
     * إنشاء كيان جديد
     */
    public function createEntity($data)
    {
        $sql = "INSERT INTO dynamic_entities (
                    company_id, module_code, entity_code, entity_name_ar, entity_name_en,
                    description_ar, description_en, icon_name, table_prefix,
                    has_workflow, has_approval, has_attachments, has_comments, has_history,
                    display_order, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['module_code'],
            $data['entity_code'],
            $data['entity_name_ar'],
            $data['entity_name_en'] ?? null,
            $data['description_ar'] ?? null,
            $data['description_en'] ?? null,
            $data['icon_name'] ?? null,
            $data['table_prefix'] ?? null,
            $data['has_workflow'] ?? 0,
            $data['has_approval'] ?? 0,
            $data['has_attachments'] ?? 0,
            $data['has_comments'] ?? 0,
            $data['has_history'] ?? 1,
            $data['display_order'] ?? 0,
            $data['created_by']
        ]);

        if ($result) {
            $this->entity_id = $this->db->lastInsertId();
            return $this->entity_id;
        }

        return false;
    }

    /**
     * إضافة حقل للكيان
     */
    public function addField($field_data)
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $sql = "INSERT INTO dynamic_fields (
                    entity_id, field_code, field_name_ar, field_name_en, field_type,
                    field_length, decimal_places, is_required, is_unique, is_indexed,
                    is_searchable, is_sortable, is_filterable, is_visible_in_list, is_visible_in_form,
                    is_readonly, default_value, validation_rules, field_options,
                    reference_entity_id, reference_display_field, display_order, group_name,
                    help_text_ar, help_text_en, placeholder_ar, placeholder_en
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $this->entity_id,
            $field_data['field_code'],
            $field_data['field_name_ar'],
            $field_data['field_name_en'] ?? null,
            $field_data['field_type'],
            $field_data['field_length'] ?? null,
            $field_data['decimal_places'] ?? null,
            $field_data['is_required'] ?? 0,
            $field_data['is_unique'] ?? 0,
            $field_data['is_indexed'] ?? 0,
            $field_data['is_searchable'] ?? 1,
            $field_data['is_sortable'] ?? 1,
            $field_data['is_filterable'] ?? 1,
            $field_data['is_visible_in_list'] ?? 1,
            $field_data['is_visible_in_form'] ?? 1,
            $field_data['is_readonly'] ?? 0,
            $field_data['default_value'] ?? null,
            isset($field_data['validation_rules']) ? json_encode($field_data['validation_rules']) : null,
            isset($field_data['field_options']) ? json_encode($field_data['field_options']) : null,
            $field_data['reference_entity_id'] ?? null,
            $field_data['reference_display_field'] ?? null,
            $field_data['display_order'] ?? 0,
            $field_data['group_name'] ?? null,
            $field_data['help_text_ar'] ?? null,
            $field_data['help_text_en'] ?? null,
            $field_data['placeholder_ar'] ?? null,
            $field_data['placeholder_en'] ?? null
        ]);

        if ($result) {
            $field_id = $this->db->lastInsertId();
            $this->loadFields(); // إعادة تحميل الحقول
            return $field_id;
        }

        return false;
    }

    /**
     * إنشاء سجل جديد
     */
    public function create($data)
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $this->db->beginTransaction();

        try {
            // إنشاء السجل الأساسي
            $sql = "INSERT INTO dynamic_data (
                        entity_id, company_id, record_code, status, created_by
                    ) VALUES (?, ?, ?, ?, ?)";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $this->entity_id,
                $data['company_id'],
                $data['record_code'] ?? $this->generateRecordCode(),
                $data['status'] ?? 'active',
                $data['created_by']
            ]);

            $record_id = $this->db->lastInsertId();

            // حفظ قيم الحقول
            foreach ($this->fields_config as $field_code => $field_config) {
                if (isset($data[$field_code])) {
                    $this->saveFieldValue($record_id, $field_config, $data[$field_code]);
                }
            }

            // حفظ في التاريخ
            $this->addHistory($record_id, null, 'create', null, json_encode($data), $data['created_by']);

            $this->db->commit();
            return $record_id;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * تحديث سجل
     */
    public function update($record_id, $data, $updated_by)
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $this->db->beginTransaction();

        try {
            // الحصول على البيانات القديمة للتاريخ
            $old_data = $this->getById($record_id);

            // تحديث السجل الأساسي
            $sql = "UPDATE dynamic_data SET updated_by = ?, updated_at = NOW() WHERE record_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$updated_by, $record_id]);

            // تحديث قيم الحقول
            foreach ($this->fields_config as $field_code => $field_config) {
                if (isset($data[$field_code])) {
                    $old_value = $old_data[$field_code] ?? null;
                    $new_value = $data[$field_code];

                    if ($old_value != $new_value) {
                        $this->saveFieldValue($record_id, $field_config, $new_value);
                        $this->addHistory($record_id, $field_config['field_id'], 'update', $old_value, $new_value, $updated_by);
                    }
                }
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * حفظ قيمة حقل
     */
    protected function saveFieldValue($record_id, $field_config, $value)
    {
        // حذف القيمة القديمة
        $sql = "DELETE FROM dynamic_field_values WHERE record_id = ? AND field_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$record_id, $field_config['field_id']]);

        // إدراج القيمة الجديدة
        $sql = "INSERT INTO dynamic_field_values (
                    record_id, field_id, field_value, field_value_numeric,
                    field_value_date, field_value_datetime, field_value_boolean
                ) VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);

        // تحديد نوع القيمة حسب نوع الحقل
        $text_value = null;
        $numeric_value = null;
        $date_value = null;
        $datetime_value = null;
        $boolean_value = null;

        switch ($field_config['field_type']) {
            case 'number':
            case 'decimal':
                $numeric_value = $value;
                $text_value = (string)$value;
                break;

            case 'date':
                $date_value = $value;
                $text_value = $value;
                break;

            case 'datetime':
                $datetime_value = $value;
                $text_value = $value;
                break;

            case 'boolean':
                $boolean_value = $value ? 1 : 0;
                $text_value = $value ? 'true' : 'false';
                break;

            default:
                $text_value = $value;
        }

        $stmt->execute([
            $record_id,
            $field_config['field_id'],
            $text_value,
            $numeric_value,
            $date_value,
            $datetime_value,
            $boolean_value
        ]);
    }

    /**
     * الحصول على سجل بالمعرف
     */
    public function getById($record_id)
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        // الحصول على السجل الأساسي
        $sql = "SELECT * FROM dynamic_data WHERE record_id = ? AND entity_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$record_id, $this->entity_id]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            return null;
        }

        // الحصول على قيم الحقول
        $sql = "SELECT f.field_code, v.field_value, v.field_value_numeric,
                       v.field_value_date, v.field_value_datetime, v.field_value_boolean
                FROM dynamic_field_values v
                JOIN dynamic_fields f ON v.field_id = f.field_id
                WHERE v.record_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$record_id]);
        $field_values = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // دمج قيم الحقول مع السجل
        foreach ($field_values as $field_value) {
            $field_code = $field_value['field_code'];
            $field_config = $this->fields_config[$field_code] ?? null;

            if ($field_config) {
                switch ($field_config['field_type']) {
                    case 'number':
                    case 'decimal':
                        $record[$field_code] = $field_value['field_value_numeric'];
                        break;
                    case 'date':
                        $record[$field_code] = $field_value['field_value_date'];
                        break;
                    case 'datetime':
                        $record[$field_code] = $field_value['field_value_datetime'];
                        break;
                    case 'boolean':
                        $record[$field_code] = $field_value['field_value_boolean'] ? true : false;
                        break;
                    default:
                        $record[$field_code] = $field_value['field_value'];
                }
            }
        }

        return $record;
    }

    /**
     * إضافة سجل في التاريخ
     */
    protected function addHistory($record_id, $field_id, $action_type, $old_value, $new_value, $created_by)
    {
        $sql = "INSERT INTO dynamic_history (
                    record_id, field_id, action_type, old_value, new_value, created_by
                ) VALUES (?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $record_id,
            $field_id,
            $action_type,
            $old_value,
            $new_value,
            $created_by
        ]);
    }

    /**
     * توليد كود سجل تلقائي
     */
    protected function generateRecordCode()
    {
        $prefix = $this->entity_config['table_prefix'] ?? 'REC';
        $timestamp = date('YmdHis');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $timestamp . '-' . $random;
    }

    /**
     * الحصول على تكوين الكيان
     */
    public function getEntityConfig()
    {
        return $this->entity_config;
    }

    /**
     * الحصول على تكوين الحقول
     */
    public function getFieldsConfig()
    {
        return $this->fields_config;
    }

    /**
     * البحث والاستعلام المرن
     */
    public function query($filters = [], $options = [])
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $sql = "SELECT d.*, ";

        // إضافة حقول القيم
        $field_selects = [];
        foreach ($this->fields_config as $field_code => $field_config) {
            switch ($field_config['field_type']) {
                case 'number':
                case 'decimal':
                    $field_selects[] = "MAX(CASE WHEN f.field_code = '{$field_code}' THEN v.field_value_numeric END) as {$field_code}";
                    break;
                case 'date':
                    $field_selects[] = "MAX(CASE WHEN f.field_code = '{$field_code}' THEN v.field_value_date END) as {$field_code}";
                    break;
                case 'datetime':
                    $field_selects[] = "MAX(CASE WHEN f.field_code = '{$field_code}' THEN v.field_value_datetime END) as {$field_code}";
                    break;
                case 'boolean':
                    $field_selects[] = "MAX(CASE WHEN f.field_code = '{$field_code}' THEN v.field_value_boolean END) as {$field_code}";
                    break;
                default:
                    $field_selects[] = "MAX(CASE WHEN f.field_code = '{$field_code}' THEN v.field_value END) as {$field_code}";
            }
        }

        $sql .= implode(', ', $field_selects);

        $sql .= " FROM dynamic_data d
                  LEFT JOIN dynamic_field_values v ON d.record_id = v.record_id
                  LEFT JOIN dynamic_fields f ON v.field_id = f.field_id
                  WHERE d.entity_id = ?";

        $params = [$this->entity_id];

        // تطبيق الفلاتر
        if (!empty($filters['company_id'])) {
            $sql .= " AND d.company_id = ?";
            $params[] = $filters['company_id'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND d.status = ?";
            $params[] = $filters['status'];
        }

        // فلاتر الحقول المخصصة
        foreach ($filters as $field_code => $value) {
            if (isset($this->fields_config[$field_code]) && $value !== null && $value !== '') {
                $field_config = $this->fields_config[$field_code];

                switch ($field_config['field_type']) {
                    case 'number':
                    case 'decimal':
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v2
                                 JOIN dynamic_fields f2 ON v2.field_id = f2.field_id
                                 WHERE v2.record_id = d.record_id
                                 AND f2.field_code = ? AND v2.field_value_numeric = ?)";
                        $params[] = $field_code;
                        $params[] = $value;
                        break;

                    case 'date':
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v2
                                 JOIN dynamic_fields f2 ON v2.field_id = f2.field_id
                                 WHERE v2.record_id = d.record_id
                                 AND f2.field_code = ? AND v2.field_value_date = ?)";
                        $params[] = $field_code;
                        $params[] = $value;
                        break;

                    case 'boolean':
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v2
                                 JOIN dynamic_fields f2 ON v2.field_id = f2.field_id
                                 WHERE v2.record_id = d.record_id
                                 AND f2.field_code = ? AND v2.field_value_boolean = ?)";
                        $params[] = $field_code;
                        $params[] = $value ? 1 : 0;
                        break;

                    default:
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v2
                                 JOIN dynamic_fields f2 ON v2.field_id = f2.field_id
                                 WHERE v2.record_id = d.record_id
                                 AND f2.field_code = ? AND v2.field_value LIKE ?)";
                        $params[] = $field_code;
                        $params[] = "%{$value}%";
                }
            }
        }

        $sql .= " GROUP BY d.record_id";

        // ترتيب النتائج
        if (!empty($options['order_by'])) {
            $order_field = $options['order_by'];
            $order_direction = $options['order_direction'] ?? 'ASC';

            if (isset($this->fields_config[$order_field])) {
                $sql .= " ORDER BY {$order_field} {$order_direction}";
            } else {
                $sql .= " ORDER BY d.{$order_field} {$order_direction}";
            }
        } else {
            $sql .= " ORDER BY d.created_at DESC";
        }

        // تحديد عدد النتائج
        if (!empty($options['limit'])) {
            $sql .= " LIMIT " . (int)$options['limit'];

            if (!empty($options['offset'])) {
                $sql .= " OFFSET " . (int)$options['offset'];
            }
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * عد السجلات
     */
    public function count($filters = [])
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $sql = "SELECT COUNT(DISTINCT d.record_id) as total
                FROM dynamic_data d
                WHERE d.entity_id = ?";

        $params = [$this->entity_id];

        // تطبيق نفس الفلاتر
        if (!empty($filters['company_id'])) {
            $sql .= " AND d.company_id = ?";
            $params[] = $filters['company_id'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND d.status = ?";
            $params[] = $filters['status'];
        }

        // فلاتر الحقول المخصصة
        foreach ($filters as $field_code => $value) {
            if (isset($this->fields_config[$field_code]) && $value !== null && $value !== '') {
                $field_config = $this->fields_config[$field_code];

                switch ($field_config['field_type']) {
                    case 'number':
                    case 'decimal':
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v
                                 JOIN dynamic_fields f ON v.field_id = f.field_id
                                 WHERE v.record_id = d.record_id
                                 AND f.field_code = ? AND v.field_value_numeric = ?)";
                        $params[] = $field_code;
                        $params[] = $value;
                        break;

                    default:
                        $sql .= " AND EXISTS (SELECT 1 FROM dynamic_field_values v
                                 JOIN dynamic_fields f ON v.field_id = f.field_id
                                 WHERE v.record_id = d.record_id
                                 AND f.field_code = ? AND v.field_value LIKE ?)";
                        $params[] = $field_code;
                        $params[] = "%{$value}%";
                }
            }
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn();
    }

    /**
     * حذف سجل
     */
    public function delete($record_id, $deleted_by)
    {
        if (!$this->entity_id) {
            throw new Exception("يجب تحميل الكيان أولاً");
        }

        $this->db->beginTransaction();

        try {
            // الحصول على البيانات للتاريخ
            $record_data = $this->getById($record_id);

            // حذف السجل (سيحذف القيم والتاريخ تلقائياً بسبب CASCADE)
            $sql = "DELETE FROM dynamic_data WHERE record_id = ? AND entity_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$record_id, $this->entity_id]);

            if ($result) {
                // إضافة سجل في التاريخ
                $this->addHistory($record_id, null, 'delete', json_encode($record_data), null, $deleted_by);
            }

            $this->db->commit();
            return $result;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
}
