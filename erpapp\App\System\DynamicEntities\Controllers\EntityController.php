<?php
namespace App\System\DynamicEntities\Controllers;

use App\Core\DynamicEntity;
use App\Core\DynamicEntityManager;
use Exception;

/**
 * متحكم الكيانات المرنة
 */
class EntityController
{
    protected $manager;
    
    public function __construct()
    {
        $this->manager = new DynamicEntityManager();
    }
    
    /**
     * عرض قائمة الكيانات
     */
    public function index()
    {
        try {
            $company_id = get_current_company_id();
            $entities = $this->manager->getCompanyEntities($company_id);
            
            $data = [
                'title' => __('الكيانات المرنة'),
                'entities' => $entities,
                'breadcrumbs' => [
                    ['name' => __('لوحة التحكم'), 'url' => '/dashboard'],
                    ['name' => __('الكيانات المرنة'), 'url' => '']
                ]
            ];
            
            return view('system/dynamic_entities/index', $data);
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في تحميل الكيانات: ') . $e->getMessage());
            return redirect('/dashboard');
        }
    }
    
    /**
     * عرض نموذج إنشاء كيان جديد
     */
    public function create()
    {
        $data = [
            'title' => __('إنشاء كيان جديد'),
            'breadcrumbs' => [
                ['name' => __('لوحة التحكم'), 'url' => '/dashboard'],
                ['name' => __('الكيانات المرنة'), 'url' => '/dynamic-entities'],
                ['name' => __('إنشاء كيان جديد'), 'url' => '']
            ]
        ];
        
        return view('system/dynamic_entities/create', $data);
    }
    
    /**
     * حفظ كيان جديد
     */
    public function store()
    {
        try {
            $data = $_POST;
            $company_id = get_current_company_id();
            $user_id = get_current_user_id();
            
            // التحقق من البيانات المطلوبة
            if (empty($data['entity_code']) || empty($data['entity_name_ar'])) {
                throw new Exception(__('كود الكيان والاسم العربي مطلوبان'));
            }
            
            // التحقق من عدم تكرار الكود
            $existing = $this->manager->getEntityByCode($data['entity_code'], $company_id);
            if ($existing) {
                throw new Exception(__('كود الكيان موجود مسبقاً'));
            }
            
            $entity = new DynamicEntity();
            $entity_id = $entity->createEntity([
                'company_id' => $company_id,
                'module_code' => $data['module_code'] ?? 'custom',
                'entity_code' => $data['entity_code'],
                'entity_name_ar' => $data['entity_name_ar'],
                'entity_name_en' => $data['entity_name_en'] ?? null,
                'description_ar' => $data['description_ar'] ?? null,
                'description_en' => $data['description_en'] ?? null,
                'icon_name' => $data['icon_name'] ?? 'fas fa-database',
                'table_prefix' => $data['table_prefix'] ?? strtoupper(substr($data['entity_code'], 0, 3)),
                'has_workflow' => isset($data['has_workflow']) ? 1 : 0,
                'has_approval' => isset($data['has_approval']) ? 1 : 0,
                'has_attachments' => isset($data['has_attachments']) ? 1 : 0,
                'has_comments' => isset($data['has_comments']) ? 1 : 0,
                'has_history' => isset($data['has_history']) ? 1 : 0,
                'display_order' => $data['display_order'] ?? 0,
                'created_by' => $user_id
            ]);
            
            if ($entity_id) {
                set_flash_message('success', __('تم إنشاء الكيان بنجاح'));
                return redirect('/dynamic-entities/' . $entity_id . '/fields');
            } else {
                throw new Exception(__('فشل في إنشاء الكيان'));
            }
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في إنشاء الكيان: ') . $e->getMessage());
            return redirect('/dynamic-entities/create');
        }
    }
    
    /**
     * عرض بيانات كيان
     */
    public function show($entity_id)
    {
        try {
            $company_id = get_current_company_id();
            
            $entity = new DynamicEntity();
            $entity->loadEntity($entity_id, $company_id);
            
            $entity_config = $entity->getEntityConfig();
            $fields_config = $entity->getFieldsConfig();
            
            // الحصول على عينة من البيانات
            $sample_data = $entity->query(['company_id' => $company_id], ['limit' => 10]);
            $total_records = $entity->count(['company_id' => $company_id]);
            
            $data = [
                'title' => $entity_config['entity_name_ar'],
                'entity_config' => $entity_config,
                'fields_config' => $fields_config,
                'sample_data' => $sample_data,
                'total_records' => $total_records,
                'breadcrumbs' => [
                    ['name' => __('لوحة التحكم'), 'url' => '/dashboard'],
                    ['name' => __('الكيانات المرنة'), 'url' => '/dynamic-entities'],
                    ['name' => $entity_config['entity_name_ar'], 'url' => '']
                ]
            ];
            
            return view('system/dynamic_entities/show', $data);
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في تحميل الكيان: ') . $e->getMessage());
            return redirect('/dynamic-entities');
        }
    }
    
    /**
     * عرض إدارة حقول الكيان
     */
    public function fields($entity_id)
    {
        try {
            $company_id = get_current_company_id();
            
            $entity = new DynamicEntity();
            $entity->loadEntity($entity_id, $company_id);
            
            $entity_config = $entity->getEntityConfig();
            $fields_config = $entity->getFieldsConfig();
            
            $data = [
                'title' => __('إدارة حقول: ') . $entity_config['entity_name_ar'],
                'entity_config' => $entity_config,
                'fields_config' => $fields_config,
                'field_types' => [
                    'text' => __('نص'),
                    'textarea' => __('نص طويل'),
                    'number' => __('رقم'),
                    'decimal' => __('رقم عشري'),
                    'date' => __('تاريخ'),
                    'datetime' => __('تاريخ ووقت'),
                    'time' => __('وقت'),
                    'boolean' => __('منطقي (نعم/لا)'),
                    'select' => __('قائمة اختيار'),
                    'multiselect' => __('قائمة متعددة'),
                    'file' => __('ملف'),
                    'image' => __('صورة'),
                    'email' => __('بريد إلكتروني'),
                    'phone' => __('رقم هاتف'),
                    'url' => __('رابط'),
                    'password' => __('كلمة مرور'),
                    'json' => __('بيانات JSON'),
                    'reference' => __('مرجع لكيان آخر')
                ],
                'breadcrumbs' => [
                    ['name' => __('لوحة التحكم'), 'url' => '/dashboard'],
                    ['name' => __('الكيانات المرنة'), 'url' => '/dynamic-entities'],
                    ['name' => $entity_config['entity_name_ar'], 'url' => '/dynamic-entities/' . $entity_id],
                    ['name' => __('إدارة الحقول'), 'url' => '']
                ]
            ];
            
            return view('system/dynamic_entities/fields', $data);
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في تحميل حقول الكيان: ') . $e->getMessage());
            return redirect('/dynamic-entities');
        }
    }
    
    /**
     * إضافة حقل جديد
     */
    public function addField($entity_id)
    {
        try {
            $data = $_POST;
            $company_id = get_current_company_id();
            
            $entity = new DynamicEntity();
            $entity->loadEntity($entity_id, $company_id);
            
            // التحقق من البيانات المطلوبة
            if (empty($data['field_code']) || empty($data['field_name_ar']) || empty($data['field_type'])) {
                throw new Exception(__('كود الحقل والاسم العربي ونوع الحقل مطلوبة'));
            }
            
            // تحضير بيانات الحقل
            $field_data = [
                'field_code' => $data['field_code'],
                'field_name_ar' => $data['field_name_ar'],
                'field_name_en' => $data['field_name_en'] ?? null,
                'field_type' => $data['field_type'],
                'field_length' => !empty($data['field_length']) ? (int)$data['field_length'] : null,
                'decimal_places' => !empty($data['decimal_places']) ? (int)$data['decimal_places'] : null,
                'is_required' => isset($data['is_required']) ? 1 : 0,
                'is_unique' => isset($data['is_unique']) ? 1 : 0,
                'is_indexed' => isset($data['is_indexed']) ? 1 : 0,
                'is_searchable' => isset($data['is_searchable']) ? 1 : 0,
                'is_sortable' => isset($data['is_sortable']) ? 1 : 0,
                'is_filterable' => isset($data['is_filterable']) ? 1 : 0,
                'is_visible_in_list' => isset($data['is_visible_in_list']) ? 1 : 0,
                'is_visible_in_form' => isset($data['is_visible_in_form']) ? 1 : 0,
                'is_readonly' => isset($data['is_readonly']) ? 1 : 0,
                'default_value' => $data['default_value'] ?? null,
                'display_order' => !empty($data['display_order']) ? (int)$data['display_order'] : 0,
                'group_name' => $data['group_name'] ?? null,
                'help_text_ar' => $data['help_text_ar'] ?? null,
                'help_text_en' => $data['help_text_en'] ?? null,
                'placeholder_ar' => $data['placeholder_ar'] ?? null,
                'placeholder_en' => $data['placeholder_en'] ?? null
            ];
            
            // معالجة خيارات الحقل للقوائم
            if ($data['field_type'] === 'select' || $data['field_type'] === 'multiselect') {
                if (!empty($data['field_options'])) {
                    $options = [];
                    $option_lines = explode("\n", $data['field_options']);
                    foreach ($option_lines as $line) {
                        $line = trim($line);
                        if (!empty($line)) {
                            if (strpos($line, '=') !== false) {
                                list($key, $value) = explode('=', $line, 2);
                                $options[trim($key)] = trim($value);
                            } else {
                                $options[$line] = $line;
                            }
                        }
                    }
                    $field_data['field_options'] = ['options' => $options];
                }
            }
            
            // معالجة قواعد التحقق
            if (!empty($data['validation_rules'])) {
                $rules = [];
                $rule_lines = explode("\n", $data['validation_rules']);
                foreach ($rule_lines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $rules[] = $line;
                    }
                }
                $field_data['validation_rules'] = $rules;
            }
            
            $field_id = $entity->addField($field_data);
            
            if ($field_id) {
                set_flash_message('success', __('تم إضافة الحقل بنجاح'));
            } else {
                throw new Exception(__('فشل في إضافة الحقل'));
            }
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في إضافة الحقل: ') . $e->getMessage());
        }
        
        return redirect('/dynamic-entities/' . $entity_id . '/fields');
    }
    
    /**
     * عرض بيانات الكيان
     */
    public function data($entity_id)
    {
        try {
            $company_id = get_current_company_id();
            
            $entity = new DynamicEntity();
            $entity->loadEntity($entity_id, $company_id);
            
            $entity_config = $entity->getEntityConfig();
            $fields_config = $entity->getFieldsConfig();
            
            // معالجة الفلاتر والبحث
            $filters = ['company_id' => $company_id];
            $options = [];
            
            if (!empty($_GET['search'])) {
                // إضافة البحث في الحقول القابلة للبحث
                foreach ($fields_config as $field_code => $field_config) {
                    if ($field_config['is_searchable']) {
                        $filters[$field_code] = $_GET['search'];
                        break; // البحث في أول حقل قابل للبحث فقط
                    }
                }
            }
            
            // ترتيب النتائج
            if (!empty($_GET['sort'])) {
                $options['order_by'] = $_GET['sort'];
                $options['order_direction'] = $_GET['direction'] ?? 'ASC';
            }
            
            // تحديد عدد النتائج والصفحة
            $page = (int)($_GET['page'] ?? 1);
            $per_page = 20;
            $options['limit'] = $per_page;
            $options['offset'] = ($page - 1) * $per_page;
            
            $records = $entity->query($filters, $options);
            $total_records = $entity->count($filters);
            $total_pages = ceil($total_records / $per_page);
            
            $data = [
                'title' => __('بيانات: ') . $entity_config['entity_name_ar'],
                'entity_config' => $entity_config,
                'fields_config' => $fields_config,
                'records' => $records,
                'total_records' => $total_records,
                'current_page' => $page,
                'total_pages' => $total_pages,
                'per_page' => $per_page,
                'breadcrumbs' => [
                    ['name' => __('لوحة التحكم'), 'url' => '/dashboard'],
                    ['name' => __('الكيانات المرنة'), 'url' => '/dynamic-entities'],
                    ['name' => $entity_config['entity_name_ar'], 'url' => '/dynamic-entities/' . $entity_id],
                    ['name' => __('البيانات'), 'url' => '']
                ]
            ];
            
            return view('system/dynamic_entities/data', $data);
            
        } catch (Exception $e) {
            set_flash_message('error', __('خطأ في تحميل بيانات الكيان: ') . $e->getMessage());
            return redirect('/dynamic-entities');
        }
    }
}
