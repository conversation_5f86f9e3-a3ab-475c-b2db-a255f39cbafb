<?php
/**
 * اختبار PHPMailer المباشر
 */

// تحميل Composer autoloader
$composer_paths = [
    __DIR__ . "/../vendor/autoload.php",
    __DIR__ . "/../../vendor/autoload.php"
];

foreach ($composer_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

// اختبار PHPMailer
if (class_exists("\PHPMailer\PHPMailer\PHPMailer")) {
    echo "✅ PHPMailer متاح\n";
    
    try {
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        echo "✅ تم إنشاء instance بنجاح\n";
        echo "📧 Version: " . $mail::VERSION . "\n";
    } catch (Exception $e) {
        echo "❌ خطأ: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PHPMailer غير متاح\n";
}
?>