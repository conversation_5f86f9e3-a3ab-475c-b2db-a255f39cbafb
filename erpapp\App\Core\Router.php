<?php
namespace App\Core;

/**
 * Router Class
 *
 * Handles routing for the application
 */
class Router {
    /**
     * Routes collection
     *
     * @var array
     */
    private $routes = [];

    /**
     * Current route parameters
     *
     * @var array
     */
    private $params = [];

    /**
     * Add a route to the routing table
     *
     * @param string $route  The route URL
     * @param array  $params Parameters (controller, action, etc.)
     *
     * @return void
     */
    public function add($route, $params = []) {
        // Convert the route to a regular expression
        $route = preg_replace('/\//', '\\/', $route);
        $route = preg_replace('/\{([a-z]+)\}/', '(?P<\1>[a-zA-Z0-9_-]+)', $route);
        $route = preg_replace('/\{([a-z]+):([^\}]+)\}/', '(?P<\1>\2)', $route);
        $route = '/^' . $route . '$/i';

        $this->routes[$route] = $params;
    }

    /**
     * Get all the routes from the routing table
     *
     * @return array
     */
    public function getRoutes() {
        return $this->routes;
    }

    /**
     * Match the route to the routes in the routing table
     *
     * @param string $url The route URL
     *
     * @return boolean  true if a match found, false otherwise
     */
    public function match($url) {
        // Remove query string from URL
        if ($pos = strpos($url, '?')) {
            $url = substr($url, 0, $pos);
        }

        // Remove trailing slash
        $url = rtrim($url, '/');

        // If URL is empty, set it to '/'
        if ($url == '') {
            $url = '/';
        }

        foreach ($this->routes as $route => $params) {
            if (preg_match($route, $url, $matches)) {
                foreach ($matches as $key => $match) {
                    if (is_string($key)) {
                        $params[$key] = $match;
                    }
                }

                $this->params = $params;
                return true;
            }
        }

        return false;
    }

    /**
     * Get the currently matched parameters
     *
     * @return array
     */
    public function getParams() {
        return $this->params;
    }

    /**
     * Dispatch the route
     *
     * @param string $url The route URL
     *
     * @return void
     */
    public function dispatch() {
        $url = $_SERVER['REQUEST_URI'];

        // Remove base path from URL
        $base_path = parse_url(APP_URL, PHP_URL_PATH);
        if ($base_path && strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }

        if ($this->match($url)) {
            $controller = $this->params['controller'];
            $controller = $this->convertToStudlyCaps($controller);
            $controller = $this->getNamespace() . $controller;

            // Try to load the controller class
            if (class_exists($controller)) {
                $controller_object = new $controller($this->params);

                $action = $this->params['action'] ?? 'index';
                $action = $this->convertToCamelCase($action);

                if (method_exists($controller_object, $action)) {
                    $controller_object->$action();
                } else {
                    throw new Exception("Method $action not found in controller $controller");
                }
            } else {
                // Try to load the controller file directly
                $controller_file = BASE_PATH . '/App/Controllers/' . $this->convertToStudlyCaps($this->params['controller']) . '.php';

                if (file_exists($controller_file)) {
                    require_once $controller_file;

                    // Try again after loading the file
                    if (class_exists($controller)) {
                        $controller_object = new $controller($this->params);

                        $action = $this->params['action'] ?? 'index';
                        $action = $this->convertToCamelCase($action);

                        if (method_exists($controller_object, $action)) {
                            $controller_object->$action();
                        } else {
                            throw new Exception("Method $action not found in controller $controller");
                        }
                    } else {
                        throw new Exception("Controller class $controller not found after loading file $controller_file");
                    }
                } else {
                    throw new Exception("Controller class $controller not found and file $controller_file does not exist");
                }
            }
        } else {
            // No route matched, show 404 page
            header("HTTP/1.0 404 Not Found");
            view('Notifications::errors/404');
        }
    }

    /**
     * Convert string with hyphens to StudlyCaps
     * e.g. post-authors => PostAuthors
     *
     * @param string $string The string to convert
     *
     * @return string
     */
    private function convertToStudlyCaps($string) {
        return str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));
    }

    /**
     * Convert string with hyphens to camelCase
     * e.g. add-new => addNew
     *
     * @param string $string The string to convert
     *
     * @return string
     */
    private function convertToCamelCase($string) {
        return lcfirst($this->convertToStudlyCaps($string));
    }

    /**
     * Get the namespace for the controller class
     *
     * @return string The namespace
     */
    private function getNamespace() {
        $namespace = 'App\\Controllers\\';

        if (array_key_exists('namespace', $this->params)) {
            $namespace .= $this->params['namespace'] . '\\';
        }

        return $namespace;
    }
}

// إنشاء alias للتوافق مع الكود القديم
if (!class_exists('Router', false)) {
    class_alias('\App\Core\Router', 'Router');
}
