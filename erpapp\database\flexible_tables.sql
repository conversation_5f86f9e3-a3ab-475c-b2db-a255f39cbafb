-- ========================================
-- نظام الجداول المرنة (Dynamic Tables)
-- ========================================

-- 1. جدول تعريف الكيانات (Entities)
CREATE TABLE `dynamic_entities` (
  `entity_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL,
  `entity_code` varchar(100) NOT NULL,
  `entity_name_ar` varchar(255) NOT NULL,
  `entity_name_en` varchar(255) DEFAULT NULL,
  `description_ar` text,
  `description_en` text,
  `icon_name` varchar(100) DEFAULT NULL,
  `table_prefix` varchar(50) DEFAULT NULL,
  `is_system_entity` tinyint(1) DEFAULT 0,
  `has_workflow` tinyint(1) DEFAULT 0,
  `has_approval` tinyint(1) DEFAULT 0,
  `has_attachments` tinyint(1) DEFAULT 0,
  `has_comments` tinyint(1) DEFAULT 0,
  `has_history` tinyint(1) DEFAULT 1,
  `display_order` int DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`entity_id`),
  UNIQUE KEY `company_entity_unique` (`company_id`, `entity_code`),
  KEY `idx_module` (`module_code`),
  KEY `idx_company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. جدول تعريف الحقول (Fields)
CREATE TABLE `dynamic_fields` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `entity_id` int NOT NULL,
  `field_code` varchar(100) NOT NULL,
  `field_name_ar` varchar(255) NOT NULL,
  `field_name_en` varchar(255) DEFAULT NULL,
  `field_type` enum('text','textarea','number','decimal','date','datetime','time','boolean','select','multiselect','file','image','email','phone','url','password','json','reference') NOT NULL,
  `field_length` int DEFAULT NULL,
  `decimal_places` int DEFAULT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `is_unique` tinyint(1) DEFAULT 0,
  `is_indexed` tinyint(1) DEFAULT 0,
  `is_searchable` tinyint(1) DEFAULT 1,
  `is_sortable` tinyint(1) DEFAULT 1,
  `is_filterable` tinyint(1) DEFAULT 1,
  `is_visible_in_list` tinyint(1) DEFAULT 1,
  `is_visible_in_form` tinyint(1) DEFAULT 1,
  `is_readonly` tinyint(1) DEFAULT 0,
  `default_value` text,
  `validation_rules` json DEFAULT NULL,
  `field_options` json DEFAULT NULL, -- للـ select options
  `reference_entity_id` int DEFAULT NULL, -- للـ reference fields
  `reference_display_field` varchar(100) DEFAULT NULL,
  `display_order` int DEFAULT 0,
  `group_name` varchar(100) DEFAULT NULL,
  `help_text_ar` text,
  `help_text_en` text,
  `placeholder_ar` varchar(255) DEFAULT NULL,
  `placeholder_en` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `entity_field_unique` (`entity_id`, `field_code`),
  KEY `idx_entity` (`entity_id`),
  KEY `idx_reference` (`reference_entity_id`),
  FOREIGN KEY (`entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE,
  FOREIGN KEY (`reference_entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. جدول البيانات المرن (Dynamic Data)
CREATE TABLE `dynamic_data` (
  `record_id` int NOT NULL AUTO_INCREMENT,
  `entity_id` int NOT NULL,
  `company_id` int NOT NULL,
  `record_code` varchar(100) DEFAULT NULL,
  `status` varchar(50) DEFAULT 'active',
  `workflow_status` varchar(50) DEFAULT NULL,
  `approval_status` enum('pending','approved','rejected') DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`record_id`),
  KEY `idx_entity` (`entity_id`),
  KEY `idx_company` (`company_id`),
  KEY `idx_status` (`status`),
  KEY `idx_record_code` (`record_code`),
  FOREIGN KEY (`entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 4. جدول قيم الحقول (Field Values)
CREATE TABLE `dynamic_field_values` (
  `value_id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `field_id` int NOT NULL,
  `field_value` longtext,
  `field_value_numeric` decimal(20,6) DEFAULT NULL,
  `field_value_date` date DEFAULT NULL,
  `field_value_datetime` datetime DEFAULT NULL,
  `field_value_boolean` tinyint(1) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`value_id`),
  UNIQUE KEY `record_field_unique` (`record_id`, `field_id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_field` (`field_id`),
  KEY `idx_numeric_value` (`field_value_numeric`),
  KEY `idx_date_value` (`field_value_date`),
  KEY `idx_datetime_value` (`field_value_datetime`),
  KEY `idx_boolean_value` (`field_value_boolean`),
  FULLTEXT KEY `idx_text_value` (`field_value`),
  FOREIGN KEY (`record_id`) REFERENCES `dynamic_data` (`record_id`) ON DELETE CASCADE,
  FOREIGN KEY (`field_id`) REFERENCES `dynamic_fields` (`field_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 5. جدول العلاقات بين الكيانات
CREATE TABLE `dynamic_relationships` (
  `relationship_id` int NOT NULL AUTO_INCREMENT,
  `parent_entity_id` int NOT NULL,
  `child_entity_id` int NOT NULL,
  `relationship_type` enum('one_to_one','one_to_many','many_to_many') NOT NULL,
  `relationship_name` varchar(100) NOT NULL,
  `foreign_key_field` varchar(100) NOT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `cascade_delete` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`relationship_id`),
  KEY `idx_parent` (`parent_entity_id`),
  KEY `idx_child` (`child_entity_id`),
  FOREIGN KEY (`parent_entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE,
  FOREIGN KEY (`child_entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 6. جدول المرفقات
CREATE TABLE `dynamic_attachments` (
  `attachment_id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `field_id` int DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `is_image` tinyint(1) DEFAULT 0,
  `uploaded_by` int NOT NULL,
  `uploaded_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`attachment_id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_field` (`field_id`),
  FOREIGN KEY (`record_id`) REFERENCES `dynamic_data` (`record_id`) ON DELETE CASCADE,
  FOREIGN KEY (`field_id`) REFERENCES `dynamic_fields` (`field_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 7. جدول التعليقات
CREATE TABLE `dynamic_comments` (
  `comment_id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `comment_text` text NOT NULL,
  `comment_type` enum('comment','note','system') DEFAULT 'comment',
  `is_private` tinyint(1) DEFAULT 0,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`comment_id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`record_id`) REFERENCES `dynamic_data` (`record_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 8. جدول تاريخ التغييرات
CREATE TABLE `dynamic_history` (
  `history_id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `field_id` int DEFAULT NULL,
  `action_type` enum('create','update','delete','approve','reject') NOT NULL,
  `old_value` text,
  `new_value` text,
  `change_reason` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`history_id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_field` (`field_id`),
  KEY `idx_action` (`action_type`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`record_id`) REFERENCES `dynamic_data` (`record_id`) ON DELETE CASCADE,
  FOREIGN KEY (`field_id`) REFERENCES `dynamic_fields` (`field_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 9. جدول فهارس البحث المخصصة
CREATE TABLE `dynamic_search_indexes` (
  `index_id` int NOT NULL AUTO_INCREMENT,
  `entity_id` int NOT NULL,
  `index_name` varchar(100) NOT NULL,
  `field_ids` json NOT NULL,
  `index_type` enum('normal','unique','fulltext','composite') DEFAULT 'normal',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`index_id`),
  KEY `idx_entity` (`entity_id`),
  FOREIGN KEY (`entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 10. جدول التقارير المخصصة
CREATE TABLE `dynamic_reports` (
  `report_id` int NOT NULL AUTO_INCREMENT,
  `entity_id` int NOT NULL,
  `company_id` int NOT NULL,
  `report_name_ar` varchar(255) NOT NULL,
  `report_name_en` varchar(255) DEFAULT NULL,
  `report_type` enum('list','summary','chart','pivot') DEFAULT 'list',
  `fields_config` json NOT NULL,
  `filters_config` json DEFAULT NULL,
  `sorting_config` json DEFAULT NULL,
  `grouping_config` json DEFAULT NULL,
  `chart_config` json DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_id`),
  KEY `idx_entity` (`entity_id`),
  KEY `idx_company` (`company_id`),
  FOREIGN KEY (`entity_id`) REFERENCES `dynamic_entities` (`entity_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
