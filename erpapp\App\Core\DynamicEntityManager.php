<?php
namespace App\Core;

use PDO;
use Exception;

/**
 * مدير الكيانات المرنة
 * يوفر واجهة سهلة لإنشاء وإدارة الكيانات المرنة
 */
class DynamicEntityManager
{
    protected $db;
    
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }
    
    /**
     * إنشاء كيان مخزون مرن
     */
    public function createInventoryEntity($company_id, $created_by)
    {
        $this->db->beginTransaction();
        
        try {
            // إنشاء كيان المنتجات
            $products_entity = new DynamicEntity();
            $products_entity_id = $products_entity->createEntity([
                'company_id' => $company_id,
                'module_code' => 'inventory',
                'entity_code' => 'products',
                'entity_name_ar' => 'المنتجات',
                'entity_name_en' => 'Products',
                'description_ar' => 'إدارة المنتجات والخدمات',
                'description_en' => 'Manage products and services',
                'icon_name' => 'fas fa-box',
                'table_prefix' => 'PRD',
                'has_attachments' => 1,
                'has_comments' => 1,
                'has_history' => 1,
                'created_by' => $created_by
            ]);
            
            // إضافة حقول المنتجات
            $products_entity->loadEntity('products', $company_id);
            
            $product_fields = [
                [
                    'field_code' => 'product_code',
                    'field_name_ar' => 'كود المنتج',
                    'field_name_en' => 'Product Code',
                    'field_type' => 'text',
                    'field_length' => 50,
                    'is_required' => 1,
                    'is_unique' => 1,
                    'is_indexed' => 1,
                    'display_order' => 1,
                    'group_name' => 'basic_info'
                ],
                [
                    'field_code' => 'product_name_ar',
                    'field_name_ar' => 'اسم المنتج (عربي)',
                    'field_name_en' => 'Product Name (Arabic)',
                    'field_type' => 'text',
                    'field_length' => 255,
                    'is_required' => 1,
                    'is_searchable' => 1,
                    'display_order' => 2,
                    'group_name' => 'basic_info'
                ],
                [
                    'field_code' => 'product_name_en',
                    'field_name_ar' => 'اسم المنتج (إنجليزي)',
                    'field_name_en' => 'Product Name (English)',
                    'field_type' => 'text',
                    'field_length' => 255,
                    'is_searchable' => 1,
                    'display_order' => 3,
                    'group_name' => 'basic_info'
                ],
                [
                    'field_code' => 'description_ar',
                    'field_name_ar' => 'الوصف (عربي)',
                    'field_name_en' => 'Description (Arabic)',
                    'field_type' => 'textarea',
                    'display_order' => 4,
                    'group_name' => 'basic_info'
                ],
                [
                    'field_code' => 'barcode',
                    'field_name_ar' => 'الباركود',
                    'field_name_en' => 'Barcode',
                    'field_type' => 'text',
                    'field_length' => 100,
                    'is_unique' => 1,
                    'display_order' => 5,
                    'group_name' => 'basic_info'
                ],
                [
                    'field_code' => 'category',
                    'field_name_ar' => 'الفئة',
                    'field_name_en' => 'Category',
                    'field_type' => 'select',
                    'field_options' => [
                        'options' => [
                            'electronics' => 'إلكترونيات',
                            'clothing' => 'ملابس',
                            'food' => 'أغذية',
                            'books' => 'كتب',
                            'furniture' => 'أثاث',
                            'tools' => 'أدوات',
                            'other' => 'أخرى'
                        ]
                    ],
                    'is_filterable' => 1,
                    'display_order' => 6,
                    'group_name' => 'classification'
                ],
                [
                    'field_code' => 'unit',
                    'field_name_ar' => 'وحدة القياس',
                    'field_name_en' => 'Unit of Measure',
                    'field_type' => 'select',
                    'field_options' => [
                        'options' => [
                            'piece' => 'قطعة',
                            'kg' => 'كيلوجرام',
                            'liter' => 'لتر',
                            'meter' => 'متر',
                            'box' => 'صندوق',
                            'pack' => 'عبوة'
                        ]
                    ],
                    'is_required' => 1,
                    'display_order' => 7,
                    'group_name' => 'classification'
                ],
                [
                    'field_code' => 'cost_price',
                    'field_name_ar' => 'سعر التكلفة',
                    'field_name_en' => 'Cost Price',
                    'field_type' => 'decimal',
                    'decimal_places' => 2,
                    'is_required' => 1,
                    'display_order' => 8,
                    'group_name' => 'pricing'
                ],
                [
                    'field_code' => 'selling_price',
                    'field_name_ar' => 'سعر البيع',
                    'field_name_en' => 'Selling Price',
                    'field_type' => 'decimal',
                    'decimal_places' => 2,
                    'is_required' => 1,
                    'display_order' => 9,
                    'group_name' => 'pricing'
                ],
                [
                    'field_code' => 'min_stock_level',
                    'field_name_ar' => 'الحد الأدنى للمخزون',
                    'field_name_en' => 'Minimum Stock Level',
                    'field_type' => 'number',
                    'default_value' => '0',
                    'display_order' => 10,
                    'group_name' => 'inventory'
                ],
                [
                    'field_code' => 'max_stock_level',
                    'field_name_ar' => 'الحد الأقصى للمخزون',
                    'field_name_en' => 'Maximum Stock Level',
                    'field_type' => 'number',
                    'default_value' => '1000',
                    'display_order' => 11,
                    'group_name' => 'inventory'
                ],
                [
                    'field_code' => 'current_stock',
                    'field_name_ar' => 'المخزون الحالي',
                    'field_name_en' => 'Current Stock',
                    'field_type' => 'number',
                    'default_value' => '0',
                    'is_readonly' => 1,
                    'display_order' => 12,
                    'group_name' => 'inventory'
                ],
                [
                    'field_code' => 'track_inventory',
                    'field_name_ar' => 'تتبع المخزون',
                    'field_name_en' => 'Track Inventory',
                    'field_type' => 'boolean',
                    'default_value' => 'true',
                    'display_order' => 13,
                    'group_name' => 'inventory'
                ],
                [
                    'field_code' => 'weight',
                    'field_name_ar' => 'الوزن (كجم)',
                    'field_name_en' => 'Weight (kg)',
                    'field_type' => 'decimal',
                    'decimal_places' => 3,
                    'display_order' => 14,
                    'group_name' => 'physical'
                ],
                [
                    'field_code' => 'dimensions',
                    'field_name_ar' => 'الأبعاد',
                    'field_name_en' => 'Dimensions',
                    'field_type' => 'text',
                    'field_length' => 100,
                    'placeholder_ar' => 'الطول × العرض × الارتفاع',
                    'placeholder_en' => 'Length × Width × Height',
                    'display_order' => 15,
                    'group_name' => 'physical'
                ],
                [
                    'field_code' => 'tax_rate',
                    'field_name_ar' => 'معدل الضريبة (%)',
                    'field_name_en' => 'Tax Rate (%)',
                    'field_type' => 'decimal',
                    'decimal_places' => 2,
                    'default_value' => '15',
                    'display_order' => 16,
                    'group_name' => 'pricing'
                ],
                [
                    'field_code' => 'is_active',
                    'field_name_ar' => 'نشط',
                    'field_name_en' => 'Active',
                    'field_type' => 'boolean',
                    'default_value' => 'true',
                    'display_order' => 17,
                    'group_name' => 'status'
                ],
                [
                    'field_code' => 'notes',
                    'field_name_ar' => 'ملاحظات',
                    'field_name_en' => 'Notes',
                    'field_type' => 'textarea',
                    'display_order' => 18,
                    'group_name' => 'additional'
                ]
            ];
            
            foreach ($product_fields as $field) {
                $products_entity->addField($field);
            }
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم إنشاء كيان المخزون المرن بنجاح',
                'entity_id' => $products_entity_id
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء كيان مبيعات مرن
     */
    public function createSalesEntity($company_id, $created_by)
    {
        $this->db->beginTransaction();
        
        try {
            // إنشاء كيان العملاء
            $customers_entity = new DynamicEntity();
            $customers_entity_id = $customers_entity->createEntity([
                'company_id' => $company_id,
                'module_code' => 'sales',
                'entity_code' => 'customers',
                'entity_name_ar' => 'العملاء',
                'entity_name_en' => 'Customers',
                'description_ar' => 'إدارة بيانات العملاء',
                'description_en' => 'Manage customer data',
                'icon_name' => 'fas fa-users',
                'table_prefix' => 'CUST',
                'has_attachments' => 1,
                'has_comments' => 1,
                'has_history' => 1,
                'created_by' => $created_by
            ]);
            
            // إضافة حقول العملاء
            $customers_entity->loadEntity('customers', $company_id);
            
            $customer_fields = [
                [
                    'field_code' => 'customer_code',
                    'field_name_ar' => 'كود العميل',
                    'field_name_en' => 'Customer Code',
                    'field_type' => 'text',
                    'field_length' => 50,
                    'is_required' => 1,
                    'is_unique' => 1,
                    'is_indexed' => 1,
                    'display_order' => 1
                ],
                [
                    'field_code' => 'customer_name',
                    'field_name_ar' => 'اسم العميل',
                    'field_name_en' => 'Customer Name',
                    'field_type' => 'text',
                    'field_length' => 255,
                    'is_required' => 1,
                    'is_searchable' => 1,
                    'display_order' => 2
                ],
                [
                    'field_code' => 'email',
                    'field_name_ar' => 'البريد الإلكتروني',
                    'field_name_en' => 'Email',
                    'field_type' => 'email',
                    'field_length' => 255,
                    'is_unique' => 1,
                    'display_order' => 3
                ],
                [
                    'field_code' => 'phone',
                    'field_name_ar' => 'رقم الهاتف',
                    'field_name_en' => 'Phone Number',
                    'field_type' => 'phone',
                    'field_length' => 20,
                    'display_order' => 4
                ],
                [
                    'field_code' => 'address',
                    'field_name_ar' => 'العنوان',
                    'field_name_en' => 'Address',
                    'field_type' => 'textarea',
                    'display_order' => 5
                ],
                [
                    'field_code' => 'city',
                    'field_name_ar' => 'المدينة',
                    'field_name_en' => 'City',
                    'field_type' => 'text',
                    'field_length' => 100,
                    'display_order' => 6
                ],
                [
                    'field_code' => 'country',
                    'field_name_ar' => 'الدولة',
                    'field_name_en' => 'Country',
                    'field_type' => 'select',
                    'field_options' => [
                        'options' => [
                            'SA' => 'السعودية',
                            'AE' => 'الإمارات',
                            'KW' => 'الكويت',
                            'QA' => 'قطر',
                            'BH' => 'البحرين',
                            'OM' => 'عمان',
                            'JO' => 'الأردن',
                            'LB' => 'لبنان',
                            'EG' => 'مصر'
                        ]
                    ],
                    'display_order' => 7
                ],
                [
                    'field_code' => 'customer_type',
                    'field_name_ar' => 'نوع العميل',
                    'field_name_en' => 'Customer Type',
                    'field_type' => 'select',
                    'field_options' => [
                        'options' => [
                            'individual' => 'فرد',
                            'company' => 'شركة',
                            'government' => 'جهة حكومية'
                        ]
                    ],
                    'is_required' => 1,
                    'display_order' => 8
                ],
                [
                    'field_code' => 'credit_limit',
                    'field_name_ar' => 'حد الائتمان',
                    'field_name_en' => 'Credit Limit',
                    'field_type' => 'decimal',
                    'decimal_places' => 2,
                    'default_value' => '0',
                    'display_order' => 9
                ],
                [
                    'field_code' => 'payment_terms',
                    'field_name_ar' => 'شروط الدفع (أيام)',
                    'field_name_en' => 'Payment Terms (Days)',
                    'field_type' => 'number',
                    'default_value' => '30',
                    'display_order' => 10
                ]
            ];
            
            foreach ($customer_fields as $field) {
                $customers_entity->addField($field);
            }
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم إنشاء كيان المبيعات المرن بنجاح',
                'entity_id' => $customers_entity_id
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * الحصول على جميع الكيانات للشركة
     */
    public function getCompanyEntities($company_id)
    {
        $sql = "SELECT * FROM dynamic_entities 
                WHERE company_id = ? AND is_active = 1 
                ORDER BY module_code, display_order, entity_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على كيان بالكود
     */
    public function getEntityByCode($entity_code, $company_id)
    {
        $sql = "SELECT * FROM dynamic_entities 
                WHERE entity_code = ? AND company_id = ? AND is_active = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_code, $company_id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
