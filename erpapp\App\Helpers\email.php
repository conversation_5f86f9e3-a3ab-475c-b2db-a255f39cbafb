<?php
/**
 * Email Helper Functions - Updated for External Libraries
 * 
 * هذا الملف محدث للعمل مع المكتبات الخارجية (Composer + PHPMailer)
 */

// التأكد من تحميل Composer autoloader
if (!class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
    // محاولة تحميل Composer autoloader إذا لم يكن محملاً
 
    
    foreach ($composer_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            error_log("Composer autoloader loaded from email.php: $path");
            break;
        }
    }
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * إرسال إيميل باستخدام PHPMailer
 *
 * @param string $to عنوان المستلم
 * @param string $subject موضوع الإيميل
 * @param string $body محتوى الإيميل (HTML)
 * @param string $altBody محتوى بديل (نص عادي)
 * @param array $attachments مرفقات الإيميل
 * @return bool true إذا تم الإرسال بنجاح، false إذا فشل
 */
function send_email($to, $subject, $body, $altBody = '', $attachments = []) {
    // التحقق من توفر PHPMailer
    if (!class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
        error_log('ERROR: PHPMailer class not found. Make sure Composer autoloader is loaded.');
        return send_email_fallback($to, $subject, $body);
    }

    $mail = new PHPMailer(true);

    try {
        // إعدادات الخادم
        $mail->isSMTP();
        $mail->Host       = defined('MAIL_HOST') ? MAIL_HOST : env('MAIL_HOST', 'smtp.gmail.com');
        $mail->SMTPAuth   = true;
        $mail->Username   = defined('MAIL_USERNAME') ? MAIL_USERNAME : env('MAIL_USERNAME', '');
        $mail->Password   = defined('MAIL_PASSWORD') ? MAIL_PASSWORD : env('MAIL_PASSWORD', '');
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = defined('MAIL_PORT') ? MAIL_PORT : env('MAIL_PORT', 587);

        // إعدادات المرسل والمستلم
        $fromAddress = defined('MAIL_FROM_ADDRESS') ? MAIL_FROM_ADDRESS : env('MAIL_FROM_ADDRESS', '<EMAIL>');
        $fromName = defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : env('MAIL_FROM_NAME', 'ERP System');
        
        $mail->setFrom($fromAddress, $fromName);
        $mail->addAddress($to);

        // محتوى الإيميل
        $mail->isHTML(true);
        $mail->CharSet = 'UTF-8';
        $mail->Subject = $subject;
        $mail->Body    = $body;
        $mail->AltBody = $altBody ?: strip_tags($body);

        // إضافة المرفقات
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // إرسال الإيميل
        $result = $mail->send();
        
        if ($result) {
            error_log("Email sent successfully to: $to with subject: $subject");
        }
        
        return $result;
        
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('PHPMailer Error: ' . $mail->ErrorInfo);
        error_log('PHPMailer Exception: ' . $e->getMessage());
        
        // محاولة الإرسال البديل
        return send_email_fallback($to, $subject, $body);
    }
}

/**
 * إرسال إيميل بديل باستخدام دالة mail() المدمجة
 */
function send_email_fallback($to, $subject, $body) {
    try {
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $fromAddress = defined('MAIL_FROM_ADDRESS') ? MAIL_FROM_ADDRESS : env('MAIL_FROM_ADDRESS', '<EMAIL>');
        $fromName = defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : env('MAIL_FROM_NAME', 'ERP System');
        $headers .= "From: $fromName <$fromAddress>" . "\r\n";
        
        $result = mail($to, $subject, $body, $headers);
        
        if ($result) {
            error_log("Fallback email sent successfully to: $to");
        } else {
            error_log("Fallback email failed to: $to");
        }
        
        return $result;
        
    } catch (Exception $e) {
        error_log('Fallback email error: ' . $e->getMessage());
        return false;
    }
}

/**
 * إرسال إيميل نسيان كلمة المرور
 *
 * @param string $email عنوان الإيميل
 * @param string $token رمز إعادة التعيين
 * @param string $language اللغة (ar أو en)
 * @return bool true إذا تم الإرسال بنجاح، false إذا فشل
 */
function send_password_reset_email($email, $token, $language = 'ar') {
    // تسجيل محاولة الإرسال
    error_log("Attempting to send password reset email to: $email with token: " . substr($token, 0, 16) . "... in language: $language");
    
    // تحديد اللغة
    if ($language == 'العربية') {
        $language = 'ar';
    } elseif ($language == 'English') {
        $language = 'en';
    }

    // إنشاء رابط إعادة التعيين
    $app_url = defined('APP_URL') ? APP_URL : env('APP_URL', 'http://localhost');
    $reset_link = rtrim($app_url, '/') . '/reset-password/' . $token;

    // محتوى الإيميل حسب اللغة
    if ($language == 'en') {
        // English content
        $subject = "Password Reset Request";
        $title = "Password Reset";
        $message1 = "You have requested to reset your password.";
        $message2 = "Click the button below to reset your password:";
        $button_text = "Reset Password";
        $message3 = "This link will expire in 1 hour.";
        $message4 = "If you did not request this, please ignore this email.";
        $dir = "ltr";
        $align = "left";
    } else {
        // Arabic content
        $subject = "استعادة كلمة المرور";
        $title = "استعادة كلمة المرور";
        $message1 = "لقد طلبت إعادة تعيين كلمة المرور الخاصة بك.";
        $message2 = "انقر على الزر أدناه لإعادة تعيين كلمة المرور:";
        $button_text = "إعادة تعيين كلمة المرور";
        $message3 = "ينتهي هذا الرابط خلال ساعة واحدة.";
        $message4 = "إذا لم تطلب هذا، يرجى تجاهل هذا الإيميل.";
        $dir = "rtl";
        $align = "right";
    }

    // تصميم الإيميل
    $body = "
    <div dir=\"$dir\" style=\"font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;\">
        <div style=\"background-color: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 600px; margin: 0 auto;\">
            
            <!-- Header -->
            <div style=\"text-align: center; margin-bottom: 40px; border-bottom: 3px solid #667eea; padding-bottom: 20px;\">
                <h1 style=\"color: #667eea; margin: 0; font-size: 28px;\">🔐 $title</h1>
                <p style=\"color: #666; margin: 10px 0 0 0; font-size: 16px;\">نظام إدارة موارد المؤسسات</p>
            </div>
            
            <!-- Content -->
            <div style=\"text-align: $align; margin: 30px 0;\">
                <p style=\"font-size: 16px; line-height: 1.6; color: #333;\">$message1</p>
                <p style=\"font-size: 16px; line-height: 1.6; color: #333;\">$message2</p>
                
                <div style=\"text-align: center; margin: 40px 0;\">
                    <a href=\"$reset_link\"
                       style=\"background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
                              color: white;
                              padding: 15px 40px;
                              text-decoration: none;
                              border-radius: 25px;
                              display: inline-block;
                              font-weight: bold;
                              font-size: 16px;
                              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\">
                        $button_text
                    </a>
                </div>
                
                <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #ffc107; margin: 20px 0;\">
                    <p style=\"margin: 0; color: #856404; font-size: 14px;\">⚠️ $message3</p>
                    <p style=\"margin: 10px 0 0 0; color: #856404; font-size: 14px;\">$message4</p>
                </div>
                
                <p style=\"font-size: 14px; color: #666; margin-top: 20px;\">
                    إذا لم يعمل الزر، يمكنك نسخ ولصق الرابط التالي في متصفحك:
                </p>
                <p style=\"background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all; font-family: monospace; font-size: 12px;\">
                    $reset_link
                </p>
            </div>
            
            <!-- Footer -->
            <hr style=\"border: none; border-top: 2px solid #eee; margin: 40px 0 20px 0;\">
            <div style=\"text-align: center; color: #666; font-size: 12px;\">
                <p style=\"margin: 5px 0;\">تم إرسال هذا الإيميل من نظام إدارة موارد المؤسسات</p>
                <p style=\"margin: 5px 0;\">الوقت: " . date('Y-m-d H:i:s') . "</p>
                <p style=\"margin: 5px 0;\">🌐 الموقع: $app_url</p>
            </div>
        </div>
    </div>";

    // إرسال الإيميل
    $result = send_email($email, $subject, $body);
    
    if ($result) {
        error_log("Password reset email sent successfully to: $email");
    } else {
        error_log("Failed to send password reset email to: $email");
    }
    
    return $result;
}

/**
 * دالة مساعدة للحصول على قيمة متغير البيئة
 */
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = getenv($key);
        if ($value === false) {
            return isset($_ENV[$key]) ? $_ENV[$key] : $default;
        }
        return $value;
    }
}
?>
