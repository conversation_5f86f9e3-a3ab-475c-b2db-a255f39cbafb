<?php
/**
 * Email Helper Functions - Clean Version
 */

/**
 * Send email using PHPMailer
 */
function send_email($to, $subject, $body, $altBody = "", $attachments = []) {
    // التحقق من وجود PHPMailer
    if (!class_exists("\PHPMailer\PHPMailer\PHPMailer")) {
        error_log("PHPMailer class not found. Make sure Composer autoloader is loaded.");
        return false;
    }

    try {
        // استخدام PHPMailer من Composer autoloader
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings
        $mail->CharSet = "UTF-8";
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = MAIL_PORT;

        // إعدادات SSL إضافية لحل مشكلة الشهادة
        $mail->SMTPOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
                "allow_self_signed" => true
            )
        );

        // Recipients
        $mail->setFrom(MAIL_FROM_ADDRESS, MAIL_FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        if (!empty($altBody)) {
            $mail->AltBody = $altBody;
        }

        // Attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // Send the email
        $mail->send();
        return true;
    } catch (\PHPMailer\PHPMailer\Exception $e) {
        error_log("PHPMailer Error: " . $e->getMessage());
        return false;
    } catch (\Exception $e) {
        error_log("General email sending error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send password reset email
 */
function send_password_reset_email($email, $token, $language = "ar") {
    // Set language
    if ($language == "العربية") {
        $language = "ar";
    } elseif ($language == "English") {
        $language = "en";
    }

    // Generate reset link
    $reset_link = defined('APP_URL') ? rtrim(APP_URL, '/') . '/reset-password/' . $token : 'http://localhost/reset-password/' . $token;

    // Email content based on language
    if ($language == "ar") {
        $subject = "استعادة كلمة المرور";
        $title = "استعادة كلمة المرور";
        $message1 = "لقد تلقينا طلبًا لإعادة تعيين كلمة المرور الخاصة بك.";
        $message2 = "لإعادة تعيين كلمة المرور، انقر على الزر أدناه:";
        $button_text = "إعادة تعيين كلمة المرور";
        $message3 = "ينتهي هذا الرابط خلال ساعة واحدة من وقت إرساله.";
        $dir = "rtl";
        $align = "right";
    } else {
        $subject = "Password Reset";
        $title = "Password Reset";
        $message1 = "We received a request to reset your password.";
        $message2 = "To reset your password, click the button below:";
        $button_text = "Reset Password";
        $message3 = "This link expires within one hour of being sent.";
        $dir = "ltr";
        $align = "left";
    }

    // Email body
    $body = "
    <div dir=\"$dir\" style=\"font-family: Arial, sans-serif; padding: 20px; text-align: $align;\">
        <h2>$title</h2>
        <p>$message1</p>
        <p>$message2</p>
        <p style=\"text-align: center; margin: 30px 0;\">
            <a href=\"$reset_link\"
               style=\"background-color: #2196F3;
                      color: white;
                      padding: 12px 30px;
                      text-decoration: none;
                      border-radius: 5px;
                      display: inline-block;\">
                $button_text
            </a>
        </p>
        <p>$message3</p>
        <hr style=\"margin: 30px 0;\">
        <p style=\"font-size: 12px; color: #666;\">
            Link: $reset_link
        </p>
    </div>";

    // Send email
    return send_email($email, $subject, $body);
}
?>