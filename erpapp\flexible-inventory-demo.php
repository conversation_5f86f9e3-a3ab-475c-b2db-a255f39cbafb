<?php
/**
 * مثال عملي - تحويل وحدة المخزون للنظام المرن
 */

echo "🚀 مثال عملي - النظام المرن للمخزون\n";
echo str_repeat("=", 60) . "\n";

// تحديد BASE_PATH
if (!defined('BASE_PATH')) {
    define('BASE_PATH', __DIR__);
}

// تحميل النظام
require_once BASE_PATH . '/loader_new.php';

// تحميل الكلاسات المرنة
require_once BASE_PATH . '/App/Core/DynamicEntity.php';
require_once BASE_PATH . '/App/Core/DynamicEntityManager.php';

use App\Core\DynamicEntity;
use App\Core\DynamicEntityManager;

echo "📊 1. إنشاء كيان المخزون المرن:\n";
echo str_repeat("-", 40) . "\n";

try {
    $company_id = 1; // معرف الشركة
    $user_id = 1;    // معرف المستخدم
    
    $manager = new DynamicEntityManager();
    
    // إنشاء كيان المنتجات المرن
    $result = $manager->createInventoryEntity($company_id, $user_id);
    
    if ($result['success']) {
        echo "✅ " . $result['message'] . "\n";
        echo "🆔 معرف الكيان: " . $result['entity_id'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الكيان: " . $e->getMessage() . "\n";
}

echo "\n📦 2. إضافة منتجات باستخدام النظام المرن:\n";
echo str_repeat("-", 40) . "\n";

try {
    // تحميل كيان المنتجات
    $products_entity = new DynamicEntity('products', $company_id);
    
    // إضافة منتج جديد
    $product_data = [
        'company_id' => $company_id,
        'created_by' => $user_id,
        'product_code' => 'LAPTOP-001',
        'product_name_ar' => 'لابتوب ديل XPS 13',
        'product_name_en' => 'Dell XPS 13 Laptop',
        'description_ar' => 'لابتوب عالي الأداء للأعمال والتصميم',
        'barcode' => '1234567890123',
        'category' => 'electronics',
        'unit' => 'piece',
        'cost_price' => 3500.00,
        'selling_price' => 4200.00,
        'min_stock_level' => 5,
        'max_stock_level' => 50,
        'current_stock' => 25,
        'track_inventory' => true,
        'weight' => 1.2,
        'dimensions' => '30.2 × 19.9 × 1.4 سم',
        'tax_rate' => 15.00,
        'is_active' => true,
        'notes' => 'منتج عالي الجودة مع ضمان سنتين'
    ];
    
    $product_id = $products_entity->create($product_data);
    
    if ($product_id) {
        echo "✅ تم إضافة المنتج بنجاح\n";
        echo "🆔 معرف المنتج: $product_id\n";
        echo "📦 كود المنتج: " . $product_data['product_code'] . "\n";
        echo "📝 اسم المنتج: " . $product_data['product_name_ar'] . "\n";
    }
    
    // إضافة منتج آخر
    $product_data2 = [
        'company_id' => $company_id,
        'created_by' => $user_id,
        'product_code' => 'MOUSE-001',
        'product_name_ar' => 'ماوس لوجيتك MX Master 3',
        'product_name_en' => 'Logitech MX Master 3 Mouse',
        'description_ar' => 'ماوس لاسلكي متقدم للمحترفين',
        'barcode' => '1234567890124',
        'category' => 'electronics',
        'unit' => 'piece',
        'cost_price' => 250.00,
        'selling_price' => 320.00,
        'min_stock_level' => 10,
        'max_stock_level' => 100,
        'current_stock' => 45,
        'track_inventory' => true,
        'weight' => 0.14,
        'dimensions' => '12.6 × 8.4 × 5.1 سم',
        'tax_rate' => 15.00,
        'is_active' => true,
        'notes' => 'ماوس عالي الدقة مع بطارية تدوم شهرين'
    ];
    
    $product_id2 = $products_entity->create($product_data2);
    
    if ($product_id2) {
        echo "✅ تم إضافة المنتج الثاني بنجاح\n";
        echo "🆔 معرف المنتج: $product_id2\n";
        echo "📦 كود المنتج: " . $product_data2['product_code'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إضافة المنتج: " . $e->getMessage() . "\n";
}

echo "\n🔍 3. البحث والاستعلام المرن:\n";
echo str_repeat("-", 40) . "\n";

try {
    // البحث في جميع المنتجات
    $all_products = $products_entity->query([
        'company_id' => $company_id,
        'status' => 'active'
    ]);
    
    echo "📊 إجمالي المنتجات: " . count($all_products) . "\n";
    
    foreach ($all_products as $product) {
        echo "- " . $product['product_name_ar'] . " (كود: " . $product['product_code'] . ")\n";
        echo "  💰 سعر البيع: " . number_format($product['selling_price'], 2) . " ريال\n";
        echo "  📦 المخزون: " . $product['current_stock'] . " " . $product['unit'] . "\n";
        echo "  ⚖️ الوزن: " . $product['weight'] . " كجم\n\n";
    }
    
    // البحث بفلتر الفئة
    $electronics = $products_entity->query([
        'company_id' => $company_id,
        'category' => 'electronics'
    ]);
    
    echo "🔌 منتجات الإلكترونيات: " . count($electronics) . "\n";
    
    // البحث بفلتر السعر
    $expensive_products = $products_entity->query([
        'company_id' => $company_id
    ], [
        'order_by' => 'selling_price',
        'order_direction' => 'DESC'
    ]);
    
    echo "💎 أغلى المنتجات:\n";
    foreach ($expensive_products as $product) {
        echo "- " . $product['product_name_ar'] . ": " . number_format($product['selling_price'], 2) . " ريال\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في البحث: " . $e->getMessage() . "\n";
}

echo "\n✏️ 4. تحديث المنتجات:\n";
echo str_repeat("-", 40) . "\n";

try {
    // تحديث سعر المنتج الأول
    $update_result = $products_entity->update($product_id, [
        'selling_price' => 3999.00,
        'current_stock' => 20,
        'notes' => 'تم تحديث السعر - عرض خاص'
    ], $user_id);
    
    if ($update_result) {
        echo "✅ تم تحديث المنتج بنجاح\n";
        
        // عرض المنتج المحدث
        $updated_product = $products_entity->getById($product_id);
        echo "📝 السعر الجديد: " . number_format($updated_product['selling_price'], 2) . " ريال\n";
        echo "📦 المخزون الجديد: " . $updated_product['current_stock'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في التحديث: " . $e->getMessage() . "\n";
}

echo "\n📈 5. إحصائيات المخزون المرن:\n";
echo str_repeat("-", 40) . "\n";

try {
    // عد المنتجات
    $total_products = $products_entity->count(['company_id' => $company_id]);
    echo "📊 إجمالي المنتجات: $total_products\n";
    
    // عد المنتجات النشطة
    $active_products = $products_entity->count([
        'company_id' => $company_id,
        'is_active' => true
    ]);
    echo "✅ المنتجات النشطة: $active_products\n";
    
    // عد منتجات الإلكترونيات
    $electronics_count = $products_entity->count([
        'company_id' => $company_id,
        'category' => 'electronics'
    ]);
    echo "🔌 منتجات الإلكترونيات: $electronics_count\n";
    
    // حساب إجمالي قيمة المخزون
    $all_products = $products_entity->query(['company_id' => $company_id]);
    $total_value = 0;
    $total_stock = 0;
    
    foreach ($all_products as $product) {
        $total_value += $product['current_stock'] * $product['selling_price'];
        $total_stock += $product['current_stock'];
    }
    
    echo "💰 إجمالي قيمة المخزون: " . number_format($total_value, 2) . " ريال\n";
    echo "📦 إجمالي الكمية: $total_stock قطعة\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الإحصائيات: " . $e->getMessage() . "\n";
}

echo "\n🏗️ 6. مرونة النظام:\n";
echo str_repeat("-", 40) . "\n";

try {
    // إضافة حقل جديد للكيان
    $new_field_id = $products_entity->addField([
        'field_code' => 'warranty_period',
        'field_name_ar' => 'فترة الضمان (شهر)',
        'field_name_en' => 'Warranty Period (Months)',
        'field_type' => 'number',
        'default_value' => '12',
        'display_order' => 19,
        'group_name' => 'warranty',
        'help_text_ar' => 'فترة الضمان بالأشهر'
    ]);
    
    if ($new_field_id) {
        echo "✅ تم إضافة حقل جديد: فترة الضمان\n";
        echo "🆔 معرف الحقل: $new_field_id\n";
        
        // تحديث منتج بالحقل الجديد
        $products_entity->update($product_id, [
            'warranty_period' => 24
        ], $user_id);
        
        echo "✅ تم تحديث المنتج بفترة ضمان 24 شهر\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إضافة الحقل: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 مميزات النظام المرن:\n";

echo "\n✅ المرونة الكاملة:\n";
echo "- إضافة حقول جديدة بدون تعديل قاعدة البيانات\n";
echo "- تخصيص أنواع البيانات (نص، رقم، تاريخ، منطق، قائمة)\n";
echo "- إعدادات مرنة لكل حقل (مطلوب، فريد، قابل للبحث)\n";
echo "- تجميع الحقول في مجموعات منطقية\n";

echo "\n🔍 البحث المتقدم:\n";
echo "- فلترة بأي حقل مخصص\n";
echo "- ترتيب متعدد المعايير\n";
echo "- بحث نصي ذكي\n";
echo "- إحصائيات تلقائية\n";

echo "\n📊 إدارة البيانات:\n";
echo "- تاريخ كامل للتغييرات\n";
echo "- تعليقات ومرفقات\n";
echo "- صلاحيات مرنة\n";
echo "- نسخ احتياطي تلقائي\n";

echo "\n🚀 الأداء:\n";
echo "- فهرسة ذكية للحقول\n";
echo "- استعلامات محسنة\n";
echo "- تخزين مؤقت للبيانات\n";
echo "- دعم ملايين السجلات\n";

echo "\n💡 سهولة الاستخدام:\n";
echo "- واجهة موحدة لجميع الكيانات\n";
echo "- تكوين بصري للحقول\n";
echo "- تقارير تلقائية\n";
echo "- تصدير واستيراد مرن\n";

echo "\nتم إنشاء هذا المثال في: " . date('Y-m-d H:i:s') . "\n";
?>
