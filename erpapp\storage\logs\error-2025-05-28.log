[2025-05-28 00:13:20] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/CategoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\CategoryController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:13:24] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/StockController.php:312
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\StockController->outOfStock()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:13:28] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/ProductController.php:29
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\ProductController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:13:32] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/ProductController.php:93
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\ProductController->create()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:14:09] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:35
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\InventoryController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:14:22] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:35
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\InventoryController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:14:29] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:35
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\InventoryController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:15:35] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:109
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\InventoryController->reports()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:15:53] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:109
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\InventoryController->reports()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:16:13] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/ProductController.php:93
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\ProductController->create()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:21:55] Error: Call to undefined function Modules\Accounting\Inventory\Controllers\get_user_company_id() in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/ProductController.php:29
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): Modules\Accounting\Inventory\Controllers\ProductController->index()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:32:52] Error: Class "Modules\Accounting\Inventory\Models\Product" not found in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): Modules\Accounting\Inventory\Controllers\InventoryController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:33:02] Error: Class "Modules\Accounting\Inventory\Models\Product" not found in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): Modules\Accounting\Inventory\Controllers\InventoryController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:33:07] Error: Class "Modules\Accounting\Inventory\Models\Product" not found in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): Modules\Accounting\Inventory\Controllers\InventoryController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:33:08] Error: Class "Modules\Accounting\Inventory\Models\Product" not found in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): Modules\Accounting\Inventory\Controllers\InventoryController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:33:10] Error: Class "Modules\Accounting\Inventory\Models\Product" not found in /home1/qqoshqmy/public_html/erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php:23
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): Modules\Accounting\Inventory\Controllers\InventoryController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:44:15] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:47:44] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:48:57] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:48:59] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:48:59] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:49:08] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:49:31] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:49:36] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:51:48] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:51:56] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:52:24] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:52:31] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:55:28] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:55:36] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:56:30] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:57:06] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:57:35] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:57:39] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:58:24] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:58:34] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 00:59:57] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:06:08] Error: Cannot use object of type Closure as array in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:07:01] Error: Cannot use object of type Closure as array in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:07:33] Error: Cannot use object of type Closure as array in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:07:40] Error: Cannot use object of type Closure as array in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:08:32] Exception: Controller class App\Controllers\RedirectController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/RedirectController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:08:44] Exception: Controller class App\Controllers\RedirectController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/RedirectController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:09:08] Exception: Controller class App\Controllers\RedirectController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/RedirectController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:35:36] Error: Class "App\Modules\Inventory\Models\PDO" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Product.php:244
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Product.php(290): App\Modules\Inventory\Models\Product->getLowStockProducts(1)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(78): App\Modules\Inventory\Models\Product->getStats(1)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(48): App\Modules\Inventory\Services\InventoryService->getDashboardStats()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:38:58] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.reorder_point' in 'having clause' in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php:113
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(113): PDOStatement->execute(Array)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(82): App\Modules\Inventory\Services\InventoryService->getReorderCount(1)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(48): App\Modules\Inventory\Services\InventoryService->getDashboardStats()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:39:38] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.reorder_point' in 'having clause' in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(112): PDOStatement->execute(Array)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(81): App\Modules\Inventory\Services\InventoryService->getReorderCount(1)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(48): App\Modules\Inventory\Services\InventoryService->getDashboardStats()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:40:09] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'p.reorder_point' in 'having clause' in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php:112
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(112): PDOStatement->execute(Array)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/InventoryService.php(81): App\Modules\Inventory\Services\InventoryService->getReorderCount(1)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(48): App\Modules\Inventory\Services\InventoryService->getDashboardStats()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:44:38] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:45:02] Exception: Controller class App\Modules\Inventory\Controllers\MovementController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:45:23] Exception: Controller class App\Modules\Inventory\Controllers\StockController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:45:29] Exception: Controller class App\Modules\Inventory\Controllers\MovementController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:45:33] Exception: Controller class App\Modules\Inventory\Controllers\ReportController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:47:56] Exception: Controller class App\Modules\Inventory\Controllers\StockController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 01:48:01] Exception: Controller class App\Modules\Inventory\Controllers\WarehouseController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:05:44] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:06:18] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:07:13] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:07:44] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:09:46] Error: Class "App\Modules\Inventory\Services\ProductService" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:34
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:12:00] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:21
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(34): App\Modules\Inventory\Services\ProductService->__construct()
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:14:12] ErrorException: require_once(/home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/../Models/Category.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:19
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(19): App\Core\ExceptionHandler::handleError(2, 'require_once(/h...', '/home1/qqoshqmy...', 19)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(19): require_once()
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(34): App\Modules\Inventory\Services\ProductService->__construct()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:14:51] ErrorException: require_once(/home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/../Models/Category.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:19
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(19): App\Core\ExceptionHandler::handleError(2, 'require_once(/h...', '/home1/qqoshqmy...', 19)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(19): require_once()
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(34): App\Modules\Inventory\Services\ProductService->__construct()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:16:38] ErrorException: require_once(/home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Category.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:20
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): App\Core\ExceptionHandler::handleError(2, 'require_once(/h...', '/home1/qqoshqmy...', 20)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): require_once()
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(34): App\Modules\Inventory\Services\ProductService->__construct()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:19:51] ErrorException: require_once(/home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Category.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:20
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): App\Core\ExceptionHandler::handleError(2, 'require_once(/h...', '/home1/qqoshqmy...', 20)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): require_once()
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:19:53] ErrorException: require_once(/home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Category.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:20
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): App\Core\ExceptionHandler::handleError(2, 'require_once(/h...', '/home1/qqoshqmy...', 20)
#1 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php(20): require_once()
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:20:43] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:21
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:21:39] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:21
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:21:45] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:21
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:22:13] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Services/ProductService.php:21
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(36): App\Modules\Inventory\Services\ProductService->__construct()
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(203): App\Modules\Inventory\Controllers\ProductController->__construct(Array)
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:25:28] Error: Class "App\Modules\Inventory\Models\Category" not found in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:91
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->create()
#1 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#2 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#3 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:25:33] ErrorException: Undefined property: App\Modules\Inventory\Controllers\ProductController::$productService in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:182
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(182): App\Core\ExceptionHandler::handleError(2, 'Undefined prope...', '/home1/qqoshqmy...', 182)
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->show()
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:34:35] ErrorException: Undefined property: App\Modules\Inventory\Controllers\ProductController::$productService in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:182
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(182): App\Core\ExceptionHandler::handleError(2, 'Undefined prope...', '/home1/qqoshqmy...', 182)
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->show()
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:34:41] Exception: Controller class App\Modules\Inventory\Controllers\MovementController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:34:45] Exception: Controller class App\Modules\Inventory\Controllers\ReportController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:34:54] Exception: Controller class App\Modules\Inventory\Controllers\StockController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:34:58] Exception: Controller class App\Modules\Inventory\Controllers\WarehouseController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:36:32] ErrorException: Undefined property: App\Modules\Inventory\Controllers\ProductController::$productService in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:225
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(225): App\Core\ExceptionHandler::handleError(2, 'Undefined prope...', '/home1/qqoshqmy...', 225)
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->edit()
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 02:48:07] Exception: Controller class App\Modules\Inventory\Controllers\WarehouseController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 10:53:01] ErrorException: Undefined property: App\Modules\Inventory\Controllers\ProductController::$productService in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php:182
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(182): App\Core\ExceptionHandler::handleError(2, 'Undefined prope...', '/home1/qqoshqmy...', 182)
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->show()
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 12:54:23] TypeError: count(): Argument #1 ($value) must be of type Countable|array, null given in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Views/products/create.php:62
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(30): include()
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::prod...', Array)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(117): view('Inventory::prod...', Array)
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->create()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 12:54:29] TypeError: count(): Argument #1 ($value) must be of type Countable|array, null given in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Views/products/create.php:62
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(30): include()
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::prod...', Array)
#2 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(117): view('Inventory::prod...', Array)
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->create()
#4 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#5 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#6 {main}
--------------------------------------------------------------------------------
[2025-05-28 13:03:12] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 13:09:10] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 13:09:15] Exception: Controller class App\Controllers\CompanyController not found and file /home1/qqoshqmy/public_html/erpapp/App/Controllers/CompanyController.php does not exist in /home1/qqoshqmy/public_html/erpapp/App/Core/Router.php:151
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(83): Router->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 15:57:11] Exception: كود المنتج موجود مسبقاً in /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Models/Product.php:138
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(144): App\Modules\Inventory\Models\Product->create(Array)
#1 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->store()
#2 /home1/qqoshqmy/public_html/erpapp/loader.php(80): App\Core\ModuleRouter->dispatch()
#3 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#4 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:35:43] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(170): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:37:52] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(170): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:40:42] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(184): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:41:07] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(184): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:41:20] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(184): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 17:41:24] ErrorException: include(/home1/qqoshqmy/public_html/erpapp/App/Views/errors/403.php): Failed to open stream: No such file or directory in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:422
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): App\Core\ExceptionHandler::handleError(2, 'include(/home1/...', '/home1/qqoshqmy...', 422)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(422): include()
#2 /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php(184): PermissionManager::handleNoPermission()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(80): PermissionManager::checkCurrentRoute()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 21:59:10] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('32')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(84): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:00:29] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('32')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(84): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:01:13] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('32')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(84): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:23:01] Exception: Controller class App\Modules\Inventory\Controllers\CategoryController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:30:32] PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'cu.status_ar' in 'field list' in /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php:89
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Models/Company.php(89): PDOStatement->execute()
#1 /home1/qqoshqmy/public_html/erpapp/App/System/Companies/Controllers/CompanyController.php(66): App\System\Companies\Models\Company->getJoinedCompanies('32')
#2 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\System\Companies\Controllers\CompanyController->index()
#3 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#4 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#5 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:40:19] Exception: Controller class App\Modules\Inventory\Controllers\WarehouseController not found in /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php:213
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#1 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#2 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:50:50] Error: Call to private method PermissionManager::isModuleAllowedInSubscription() from global scope in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:1175
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(246): isModuleAllowedInSubscription('inventory', 4)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(206): isModulePath('inventory')
#2 /home1/qqoshqmy/public_html/erpapp/App/Layouts/main.php(180): getCurrentPageType()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(39): include('/home1/qqoshqmy...')
#4 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::prod...', Array)
#5 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/ProductController.php(73): view('Inventory::prod...', Array)
#6 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\ProductController->index()
#7 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#8 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#9 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:51:16] Error: Call to private method PermissionManager::isModuleAllowedInSubscription() from global scope in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:1175
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(246): isModuleAllowedInSubscription('inventory', 4)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(206): isModulePath('inventory')
#2 /home1/qqoshqmy/public_html/erpapp/App/Layouts/main.php(180): getCurrentPageType()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(39): include('/home1/qqoshqmy...')
#4 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::dash...', Array)
#5 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(74): view('Inventory::dash...', Array)
#6 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#7 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#8 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#9 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:51:46] Error: Call to private method PermissionManager::isModuleAllowedInSubscription() from global scope in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:1175
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(282): isModuleAllowedInSubscription('inventory', 4)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(242): isModulePath('inventory')
#2 /home1/qqoshqmy/public_html/erpapp/App/Layouts/main.php(180): getCurrentPageType()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(39): include('/home1/qqoshqmy...')
#4 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::dash...', Array)
#5 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(74): view('Inventory::dash...', Array)
#6 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#7 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#8 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#9 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:57:45] Error: Call to private method PermissionManager::isModuleAllowedInSubscription() from global scope in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:1175
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(282): isModuleAllowedInSubscription('inventory', 4)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(242): isModulePath('inventory')
#2 /home1/qqoshqmy/public_html/erpapp/App/Layouts/main.php(180): getCurrentPageType()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(39): include('/home1/qqoshqmy...')
#4 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::dash...', Array)
#5 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(74): view('Inventory::dash...', Array)
#6 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#7 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#8 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#9 {main}
--------------------------------------------------------------------------------
[2025-05-28 23:58:07] Error: Call to private method PermissionManager::isModuleAllowedInSubscription() from global scope in /home1/qqoshqmy/public_html/erpapp/App/Helpers/permissions.php:1175
Stack trace:
#0 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(291): isModuleAllowedInSubscription('inventory', 4)
#1 /home1/qqoshqmy/public_html/erpapp/App/Helpers/sidebar_helpers.php(251): isModulePath('inventory')
#2 /home1/qqoshqmy/public_html/erpapp/App/Layouts/main.php(180): getCurrentPageType()
#3 /home1/qqoshqmy/public_html/erpapp/App/Core/View.php(39): include('/home1/qqoshqmy...')
#4 /home1/qqoshqmy/public_html/erpapp/App/Helpers/functions.php(221): App\Core\View::render('Inventory::dash...', Array)
#5 /home1/qqoshqmy/public_html/erpapp/App/Modules/Inventory/Controllers/InventoryController.php(74): view('Inventory::dash...', Array)
#6 /home1/qqoshqmy/public_html/erpapp/App/Core/ModuleRouter.php(208): App\Modules\Inventory\Controllers\InventoryController->index()
#7 /home1/qqoshqmy/public_html/erpapp/loader.php(85): App\Core\ModuleRouter->dispatch()
#8 /home1/qqoshqmy/public_html/erpapp/index.php(10): require_once('/home1/qqoshqmy...')
#9 {main}
--------------------------------------------------------------------------------
