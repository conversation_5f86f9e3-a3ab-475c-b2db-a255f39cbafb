<?php
/**
 * Email Helper Functions
 */

/**
 * Send email using <PERSON><PERSON>Mailer
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $altBody Email body (plain text)
 * @param array $attachments Array of file paths to attach
 * @return bool True if email sent successfully, false otherwise
 */
function send_email($to, $subject, $body, $altBody = '', $attachments = []) {
    // التحقق من وجود PHPMailer
    if (!class_exists('\PHPMailer\PHPMailer\PHPMailer')) {
        error_log('PHPMailer class not found. Make sure Composer autoloader is loaded.');
        return false;
    }

    try {
        // استخدام PHPMailer من Composer autoloader
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings
        $mail->CharSet = 'UTF-8';
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = MAIL_PORT;

        // إعدادات SSL إضافية لحل مشكلة الشهادة
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Enable debug if needed (0 = off, 1 = client messages, 2 = client and server messages)
        // $mail->SMTPDebug = 2;

        // Recipients
        $mail->setFrom(MAIL_FROM_ADDRESS, MAIL_FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        if (!empty($altBody)) {
            $mail->AltBody = $altBody;
        }

        // Attachments
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // Send the email
        $mail->send();
        return true;
    } catch (\PHPMailer\PHPMailer\Exception $e) {
        // Log PHPMailer specific error
        error_log('PHPMailer Error: ' . $e->getMessage());

        // Fallback to PHP's mail function
        try {
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: " . MAIL_FROM_NAME . " <" . MAIL_FROM_ADDRESS . ">" . "\r\n";

            if (mail($to, $subject, $body, $headers)) {
                error_log('Email sent successfully using PHP mail function as fallback');
                return true;
            } else {
                error_log('Fallback email sending failed using PHP mail function');
                return false;
            }
        } catch (\Exception $e) {
            error_log('Fallback email sending failed: ' . $e->getMessage());
            return false;
        }
    } catch (\Exception $e) {
        // Log general error
        error_log('General email sending error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Send password reset email
 *
 * @param string $email Recipient email address
 * @param string $token Reset token
 * @param string $language User language (ar or en)
 * @return bool True if email sent successfully, false otherwise
 */
function send_password_reset_email($email, $token, $language = 'ar') {
    // Set language
    if ($language == 'العربية') {
        $language = 'ar';
    } elseif ($language == 'English') {
        $language = 'en';
    }

    // Set current language
    $_SESSION['lang'] = $language;

    // Generate reset link
    $reset_link = base_url('reset-password/' . $token);

    // Email subject
    $subject = __('استعادة كلمة المرور');

    // Email body
    $body = '
    <div dir="' . ($language == 'ar' ? 'rtl' : 'ltr') . '" style="font-family: Arial, sans-serif; padding: 20px; text-align: ' . ($language == 'ar' ? 'right' : 'left') . ';">
        <h2>' . __('استعادة كلمة المرور') . '</h2>
        <p>' . __('لقد تلقينا طلبًا لإعادة تعيين كلمة المرور الخاصة بك.') . '</p>
        <p>' . __('إذا لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا البريد الإلكتروني.') . '</p>
        <p>' . __('لإعادة تعيين كلمة المرور، انقر على الزر أدناه:') . '</p>
        <p style="text-align: center; margin: 30px 0;">
            <a href="' . $reset_link . '"
               style="background-color: #2196F3;
                      color: white;
                      padding: 12px 30px;
                      text-decoration: none;
                      border-radius: 5px;
                      display: inline-block;">
                ' . __('إعادة تعيين كلمة المرور') . '
            </a>
        </p>
        <p>' . __('أو يمكنك نسخ ولصق الرابط التالي في متصفحك:') . '</p>
        <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">' . $reset_link . '</p>
        <p>' . __('ينتهي هذا الرابط خلال ساعة واحدة من وقت إرساله.') . '</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
            ' . __('إذا كنت بحاجة إلى مساعدة، يرجى الرد على هذا البريد الإلكتروني.') . '
        </p>
    </div>';

    // Send email
    return send_email($email, $subject, $body);
}

/**
 * Send welcome email after registration
 *
 * @param string $email Recipient email address
 * @param string $first_name User's first name
 * @param string $language User language (ar or en)
 * @return bool True if email sent successfully, false otherwise
 */
function send_welcome_email($email, $first_name, $language = 'ar') {
    // Set language
    if ($language == 'العربية') {
        $language = 'ar';
    } elseif ($language == 'English') {
        $language = 'en';
    }

    // Set current language
    $_SESSION['lang'] = $language;

    // Generate login link
    $login_link = base_url('login');

    // Email subject
    $subject = __('مرحبًا بك في') . ' ' . APP_NAME;

    // Email body
    $body = '
    <div dir="' . ($language == 'ar' ? 'rtl' : 'ltr') . '" style="font-family: Arial, sans-serif; padding: 20px; text-align: ' . ($language == 'ar' ? 'right' : 'left') . ';">
        <h2>' . __('مرحبًا') . ' ' . $first_name . '!</h2>
        <p>' . __('شكرًا لتسجيلك في') . ' ' . APP_NAME . '.</p>
        <p>' . __('نحن سعداء بانضمامك إلينا. يمكنك الآن تسجيل الدخول واستخدام النظام.') . '</p>
        <p style="text-align: center; margin: 30px 0;">
            <a href="' . $login_link . '"
               style="background-color: #2196F3;
                      color: white;
                      padding: 12px 30px;
                      text-decoration: none;
                      border-radius: 5px;
                      display: inline-block;">
                ' . __('تسجيل الدخول') . '
            </a>
        </p>
        <p>' . __('إذا كان لديك أي أسئلة أو استفسارات، فلا تتردد في التواصل معنا.') . '</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
            ' . __('مع أطيب التحيات،') . '<br>' . APP_NAME . ' ' . __('فريق') . '
        </p>
    </div>';

    // Send email
    return send_email($email, $subject, $body);
}
