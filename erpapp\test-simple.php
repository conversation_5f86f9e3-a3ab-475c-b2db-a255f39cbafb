<?php
/**
 * اختبار بسيط للنظام
 */

echo "<h1>🧪 اختبار النظام البسيط</h1>";
echo "<hr>";

// اختبار تحميل loader_new.php
echo "<h2>1. اختبار تحميل النظام:</h2>";

try {
    // تحميل النظام
    require_once __DIR__ . '/loader_new.php';
    echo "✅ تم تحميل النظام بنجاح<br>";
    
    // اختبار الكلاسات الأساسية
    if (class_exists('PermissionManager')) {
        echo "✅ PermissionManager class متاح<br>";
        
        // اختبار Cache functions
        if (method_exists('PermissionManager', 'getCacheStats')) {
            echo "✅ Cache system متاح<br>";
            
            $stats = PermissionManager::getCacheStats();
            echo "📊 Cache Stats: " . $stats['total_cached_items'] . " عنصر<br>";
        }
    }
    
    if (class_exists('Router')) {
        echo "✅ Router class متاح<br>";
    }
    
    if (class_exists('Database')) {
        echo "✅ Database class متاح<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل النظام: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ خطأ فادح: " . $e->getMessage() . "<br>";
}

echo "<h2>2. معلومات النظام:</h2>";
echo "🐘 PHP Version: " . PHP_VERSION . "<br>";
echo "💾 Memory Usage: " . formatBytes(memory_get_usage(true)) . "<br>";
echo "📁 Base Path: " . (defined('BASE_PATH') ? BASE_PATH : 'غير محدد') . "<br>";

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "<hr>";
echo "<p><strong>✅ الاختبار مكتمل!</strong></p>";
?>
