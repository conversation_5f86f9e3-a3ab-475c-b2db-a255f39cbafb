<?php
/**
 * اختبار المكتبات الخارجية فقط (بدون قاعدة بيانات)
 */

echo "📦 اختبار المكتبات الخارجية\n";
echo str_repeat("=", 50) . "\n";

// تحميل Composer autoloader مباشرة
$composer_autoloader = dirname(__DIR__) . '/vendor/autoload.php';
if (file_exists($composer_autoloader)) {
    require_once $composer_autoloader;
    echo "✅ تم تحميل Composer autoloader\n";
} else {
    echo "❌ Composer autoloader غير موجود\n";
    exit;
}

echo "\n📋 اختبار المكتبات:\n";

// 1. اختبار PHPMailer
echo "\n1. PHPMailer:\n";
if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
    echo "✅ PHPMailer متاح\n";
    
    try {
        $mailer = new PHPMailer\PHPMailer\PHPMailer();
        echo "✅ يمكن إنشاء instance من PHPMailer\n";
        echo "📧 Version: " . $mailer::VERSION . "\n";
        
        // اختبار تكوين SMTP
        $mailer->isSMTP();
        $mailer->Host = 'smtp.gmail.com';
        $mailer->SMTPAuth = true;
        $mailer->Port = 587;
        echo "✅ تم تكوين SMTP بنجاح\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في PHPMailer: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PHPMailer غير متاح\n";
}

// 2. اختبار Monolog
echo "\n2. Monolog (Logging):\n";
if (class_exists('Monolog\\Logger')) {
    echo "✅ Monolog متاح\n";
    
    try {
        $logger = new Monolog\Logger('test');
        echo "✅ يمكن إنشاء Logger\n";
        echo "📝 Logger Name: " . $logger->getName() . "\n";
        
        // إضافة handler للاختبار
        $logger->pushHandler(new Monolog\Handler\StreamHandler('php://temp', Monolog\Level::Info));
        $logger->info('اختبار نظام السجلات');
        echo "✅ تم إنشاء سجل اختبار بنجاح\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في Monolog: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Monolog غير متاح\n";
}

// 3. اختبار PHPDotEnv
echo "\n3. PHPDotEnv:\n";
if (class_exists('Dotenv\\Dotenv')) {
    echo "✅ PHPDotEnv متاح\n";
    
    try {
        // اختبار بسيط بدون تحميل ملف
        $reflection = new ReflectionClass('Dotenv\\Dotenv');
        echo "✅ يمكن استخدام PHPDotEnv\n";
        echo "📁 Class File: " . basename($reflection->getFileName()) . "\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في PHPDotEnv: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PHPDotEnv غير متاح\n";
}

// 4. اختبار Symfony Polyfills
echo "\n4. Symfony Polyfills:\n";
if (function_exists('ctype_alnum')) {
    echo "✅ Ctype Polyfill يعمل\n";
    echo "🔤 ctype_alnum('abc123'): " . (ctype_alnum('abc123') ? 'true' : 'false') . "\n";
} else {
    echo "❌ Ctype Polyfill لا يعمل\n";
}

if (function_exists('mb_strlen')) {
    echo "✅ Mbstring Polyfill يعمل\n";
    echo "📏 mb_strlen('مرحبا'): " . mb_strlen('مرحبا') . "\n";
} else {
    echo "❌ Mbstring Polyfill لا يعمل\n";
}

// 5. اختبار PSR Log
echo "\n5. PSR Log Interface:\n";
if (interface_exists('Psr\\Log\\LoggerInterface')) {
    echo "✅ PSR Log Interface متاح\n";
} else {
    echo "❌ PSR Log Interface غير متاح\n";
}

// 6. اختبار عملي متقدم
echo "\n6. اختبار عملي متقدم:\n";

// اختبار PHPMailer مع إعدادات كاملة
if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // تكوين كامل
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'password';
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;
        $mail->CharSet = 'UTF-8';
        
        // إعداد المرسل والمستقبل (بدون إرسال فعلي)
        $mail->setFrom('<EMAIL>', 'نظام الاختبار');
        $mail->addAddress('<EMAIL>', 'المستقبل');
        
        $mail->Subject = 'اختبار النظام';
        $mail->Body = 'هذا اختبار للتأكد من عمل PHPMailer';
        
        echo "✅ تم تكوين PHPMailer بالكامل بنجاح\n";
        echo "📧 Ready to send emails\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في التكوين المتقدم: " . $e->getMessage() . "\n";
    }
}

// اختبار Monolog مع handlers متعددة
if (class_exists('Monolog\\Logger')) {
    try {
        $logger = new Monolog\Logger('advanced_test');
        
        // إضافة handlers متعددة
        $logger->pushHandler(new Monolog\Handler\StreamHandler('php://temp', Monolog\Level::Debug));
        $logger->pushHandler(new Monolog\Handler\ErrorLogHandler());
        
        // اختبار مستويات مختلفة
        $logger->debug('رسالة تصحيح');
        $logger->info('رسالة معلومات');
        $logger->warning('رسالة تحذير');
        
        echo "✅ تم اختبار Monolog مع handlers متعددة\n";
        echo "📝 Handlers count: " . count($logger->getHandlers()) . "\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الاختبار المتقدم: " . $e->getMessage() . "\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 الخلاصة:\n";

$working_libs = 0;
$total_libs = 5;

if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) $working_libs++;
if (class_exists('Monolog\\Logger')) $working_libs++;
if (class_exists('Dotenv\\Dotenv')) $working_libs++;
if (function_exists('ctype_alnum')) $working_libs++;
if (function_exists('mb_strlen')) $working_libs++;

$percentage = ($working_libs / $total_libs) * 100;

echo "📈 المكتبات العاملة: $working_libs من $total_libs (" . number_format($percentage, 1) . "%)\n";

if ($percentage == 100) {
    echo "🎉 ممتاز! جميع المكتبات تعمل بشكل صحيح\n";
    echo "✅ النظام جاهز لاستخدام المكتبات الخارجية\n";
} elseif ($percentage >= 80) {
    echo "✅ جيد جداً! معظم المكتبات تعمل\n";
} elseif ($percentage >= 60) {
    echo "⚠️ متوسط! بعض المكتبات تحتاج إصلاح\n";
} else {
    echo "❌ مشكلة! يجب إعادة تثبيت المكتبات\n";
}

echo "\nتم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "\n";
?>
